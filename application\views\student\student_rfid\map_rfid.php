<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li> Mass Map RFID</li>
</ul>
<hr>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-10 d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('student/student_menu'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a> 
                        Mass Map Student RFID
                    </h3>   
                </div>
            </div>
            <div class="col-md-12">
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-4">
                        <div class="form-group text-center hidden-xs" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                            <div class="form-horizontal">
                                <h5>Search By Student Name</h5>
                                <div class="row">
                                    <div class="col-md-8 col-md-offset-1">
                                        <input id="stdName1" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="stdName1">
                                    </div>
                                    <div class="col-md-12">
                                        <span class="help-block">Enter few letters of name and click Enter/Get</span>
                                    </div>                            
                                </div>
                            </div>
                        </div>                
                    </div>
                    <div class="col-md-4">
                        <div class="form-group text-center hidden-xs" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                            <div class="form-horizontal">
                                <h5>Search By Student Admission No</h5>
                                <div class="row">
                                    <div class="col-md-8 col-md-offset-2">
                                        <input id="admission_no" autocomplete="off" placeholder="Search by Admission No" class="form-control input-md" name="admission_no">
                                    </div>
                                    <div class="col-md-12">
                                        <span class="help-block">Enter admission number and click Get</span>
                                    </div>
                                </div>
                            </div>
                        </div>                
                    </div>
                </div>
            </div>
        </div>            
        <div class="card-body">
            <div class="form-group">
                <div class="table-container">
                    <table class="table table-bordered" id="data-table" style="display: none;">
                        <thead>
                            <tr>
                                <th>Admission Number</th>
                                <th>Name</th>
                                <th>Grade(Section)</th>
                                <th>RFID</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>    
    </div>
</div>

<div class="modal" id="mapping_modal">
    <div class="modal-dialog modal-lg modal-dialog-centered" style="width: 50%;margin-left:25%">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="modal_title"></h1>
                <span><i class="fa fa-close" style="font-size: 25px;" onclick="modal_close()"></i></span>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col" id ="data">

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        var studentListItems = <?php echo $students ?>;
        var studentTable = $('#data-table');

        $('#stdName1').on('input', function() {
            studentTable.show();
            var searchText = $(this).val().toLowerCase();
            var filteredResults = studentListItems.filter(function(item) {
                var studentName = item.name.toLowerCase();
                return studentName.startsWith(searchText);
            });
            var html = '';
            studentTable.find('tbody').empty();
            filteredResults.forEach(function(result) {
                var escapedName = result.name.replace(/'/g, "\\'");
                var html = '<button class="btn btn-primary" onclick="map_rfid(' + result.id + ', ' + (result.rfid_number ? "'" + result.rfid_number + "'" : 'null') + ', \'' + escapedName + '\')">Map</button>';
                var row = $('<tr>');
                row.append($('<td>').text(result.id));
                row.append($('<td>').text(result.name));
                row.append($('<td>').text(result.class_name + '(' + result.section_name + ')'));
                row.append($('<td>').text(result.rfid_number));
                row.append($('<td>').html(html));
                studentTable.append(row);
            });
        });


        $('#admission_no').on('input', function() {
            studentTable.show();
            var searchText = $(this).val().toLowerCase();
            var filteredResults = studentListItems.filter(function(item) {
                var studentName = item.id;
                return studentName.startsWith(searchText);
            });
            var html = '';
            studentTable.find('tbody').empty();
            filteredResults.forEach(function(result) {
                var escapedName = result.name.replace(/'/g, "\\'");
                html = `<button class="btn btn-primary" onclick=map_rfid(${result.id}, ${result.rfid_number ? "'" + result.rfid_number + "'" : null}, '${escapedName}')>Map</button>`;
                var row = $('<tr>');
                row.append($('<td>').text(result.id));
                row.append($('<td>').text(result.name));
                row.append($('<td>').text(result.class_name + '(' + result.section_name + ')'));
                row.append($('<td>').text(result.rfid_number));
                row.append($('<td>').html(html));
                studentTable.append(row);
            });
        });
    });

    function map_rfid(adm_id, rfid_number, stu_name) {
        $.ajax({
            url: '<?= base_url("student/Student_controller/get_stu_family_det")?>',
            type: 'post',
            data: {'stu_id': adm_id},
            success: function(data) {
                var data = JSON.parse(data);
                $('#mapping_modal').modal('show');
                $('#data').empty();
                $('#modal_title').text('Scan RFID for ' + stu_name + ' and his/her Family');

                var inputContainer = $('<div class="row mb-2"></div>');
                var inputType = $(`<div class="col-md-2"><span>Student</span></div>`);
                var inputName = $(`<div class="col-md-4"><input type="text" class="form-control" value="${stu_name}" readonly></div>`);
                var textBox = $(`<div class="col-md-4"><input type="text" id="txtbox_${adm_id}_student_admission" data-tb="student_admission" data-id="${adm_id}" data-relation_type="Student" data-student_id="${adm_id}" class="form-control rfid_txtbox" placeholder="RFID" value="${rfid_number ? rfid_number : ''}"></div>`);
                var checkIcon = $('<div class="col-md-1"><span class="fa fa-check"></span></div>');
                checkIcon.find('span').css({
                    display: rfid_number ? 'inline-block' : 'none',
                    fontSize: '24px',
                    color: 'green'
                });

                var delIcon = $('<div class="col-md-1"><span class="fa fa-trash-o"></span></div>');
                delIcon.find('span').css({
                    display: rfid_number ? 'inline-block' : 'none',
                    fontSize: '24px',
                    color: 'red'
                });
                delIcon.find('span').click(function() {
                    delete_rfid(adm_id, 'student_admission');
                });
                inputContainer.append(inputType);
                inputContainer.append(inputName);
                inputContainer.append(textBox);
                inputContainer.append(checkIcon);
                inputContainer.append(delIcon);
                $('#data').append(inputContainer);

                for (var i = 0; i < data.length; i++) {
                    var result = data[i];
                    var inputContainer = $('<div class="row mb-2"></div>');
                    var inputType = $(`<div class="col-md-2"><span>${result.relation_type}</span></div>`);
                    var inputName = $(`<div class="col-md-4"><input type="text" class="form-control" value="${result.first_name}" readonly></div>`);
                    var textBox = $(`<div class="col-md-4"><input type="text" id="txtbox_${result.id}_parent" data-tb="parent"
                    data-relation_type="${result.relation_type}" data-student_id="${adm_id}"
                     data-id = "${result.id}" class="form-control rfid_txtbox" placeholder="RFID" value="${result.rfid_number ? result.rfid_number : ''}"></div>`);
                    var checkIcon = $('<div class="col-md-1"><span class="fa fa-check"></span></div>');
                    checkIcon.find('span').css({
                        display: result.rfid_number ? 'inline-block' : 'none',
                        fontSize: '24px',
                        color: 'green'
                    });

                    var delIcon = $('<div class="col-md-1"><span class="fa fa-trash-o"></span></div>');
                    delIcon.find('span').css({
                        display: result.rfid_number ? 'inline-block' : 'none',
                        fontSize: '24px',
                        color: 'red'
                    });
                    delIcon.find('span').click((function(id) {
                        return function() {
                            delete_rfid(id, 'parent');
                        };
                    })(result.id));
                    inputContainer.append(inputType);
                    inputContainer.append(inputName);
                    inputContainer.append(textBox);
                    inputContainer.append(checkIcon);
                    inputContainer.append(delIcon);
                    $('#data').append(inputContainer);
                }
                var timeout;
                $('.rfid_txtbox').on('input', function() {
                    var tb_type = $(this).data('tb');
                    var id = $(this).data('id');
                    var relation_type = $(this).data('relation_type');
                    var student_id = $(this).data('student_id');
                    var rfidValue = $(this).val();
                    clearTimeout(timeout);
                    if (rfidValue !== '') {
                        timeout = setTimeout(function() {
                            store_rfid(id, rfidValue, tb_type,relation_type,student_id);
                        }, 500);
                    }
                });
            }
        });
    }

    function store_rfid(id, value, tb_type,relation_type,student_id) {
        var checkIcon = $('#txtbox_' + id + '_' + tb_type).parent().parent().find('.fa-check');
        var delIcon = $('#txtbox_' + id + '_' + tb_type).parent().parent().find('.fa-trash-o');
        $.ajax({
            url: '<?= base_url("student/Student_controller/store_rfid_to_table")?>',
            type: 'post',
            data: {'id': id, 'rfid': value, 'table': tb_type,'relation_type': relation_type,'student_id':student_id},
            success: function(data) {
                if(data == 1){
                    delIcon.css('display','block');
                    delIcon.css('color','red');
                    delIcon.css('font-size', '24px');
                    checkIcon.css('display','');
                    checkIcon.css('color','green');
                    checkIcon.css('font-size', '24px');
                }                
            }
        });
    }

    function delete_rfid(id, tb_type){
        console.log(id);
        $.ajax({
            url: '<?= base_url("student/Student_controller/delete_rfid_from_table")?>',
            type: 'post',
            data: {'id': id, 'table': tb_type},
            success: function(data) {
                if(data == 1){
                    var txtbox = $('#txtbox_' + id + '_' + tb_type).val('');
                    $('#txtbox_' + id + '_' + tb_type).parent().parent().find('.fa-check').hide();
                    $('#txtbox_' + id + '_' + tb_type).parent().parent().find('.fa-trash-o').hide();
                }
            }
        });
    }
    function modal_close(){
        $('#mapping_modal').modal('hide');
    }

</script>