<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard') ?>">Fee Dashboard</a></li>
  <li class="active">Fee Blueprint</li>
</ul>


<div class="col-md-12 " >

  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard/') ?>"><span class="fa fa-arrow-left"></span></a>Fee Blueprint</h3>
        </div>
        <div class="col-md-3 d-flex justify-content-end">
          <a href="#" class="circleButton_noBackColor backgroundColor_organge mr-2"  data-toggle="modal" data-target="#subForm_add">
            <span data-toggle="tooltip" data-placement="top" title="Create" class="fa fa-plus" style="font-size: 19px;"></span>
          </a>              
        </div> 
        </div>
      </div>
    </div>
    
    <div class="card_body">
    <div id="loader" class="loaderclass"></div>
      <div class="col-md-5" style="padding: 0;">
     
        <div class="capacity_table" id="capacity_table">

        </div>
      </div>
<!--Fee Bluprint Deatils Buttons-->
      <div class="col-md-7">

        <div class="row" id="buttonsvisibility" style="display:none" >
          <div class="card-body" >
            <div class="col-md-12">
            <div class="col-md-9">
            <button type="button" onclick="fee_blueprint_details()" class="btn btn-primary">Details</button>
            <button type="button" onclick="add_blueprint_installment()" class="btn btn-primary">Installment Type</button>
            <button type="button" onclick="fee_all_filters()" class="btn btn-primary">Filters</button>
            <button type="button" onclick="payments_mode()" class="btn btn-primary">Components & Payments Mode</button>
            </div>
            <div class="col-md-3">
              <div class="dropdown">
                <a class="btn btn-info nav-link dropdown-toggle" href="#"  role="button" aria-haspopup="true" aria-expanded="false" style="border-radius: 0.2rem;padding:8px 37px">
                  Actions
                </a>

                <div class="dropdown-content"  id="dropdown_menu">
                  
                  <!-- <a id="regen_application" onclick="add_receipt_book()" class="dropdown-item" href="javascript:void(0)"> Receipt book </a> -->
                  <a id="regen_application" onclick="html_recepit()" class="dropdown-item" href="javascript:void(0)"> Fee Recepit
                  </a>
                  <a id="regen_application" onclick="full_fee_html_recepit()" class="dropdown-item" href="javascript:void(0)"> Full Fee Recepit
                  </a>

                  <a id="regen_application" onclick="terms_condition()" class="dropdown-item" href="javascript:void(0)"> Terms & conditions
                  </a>
                  
                  <a id="regen_application" onclick="view_component()" class="dropdown-item" href="javascript:void(0)"> View Component
                  </a>
                </div>
              </div>
              </div>
              </div>


          </div>
        </div>

        <!--End Fee Bluprint Deatils Buttons-->


        <!--Bluprint Details Table-->
        <div class="form-group " id="fee_blueprint_details" style="display:none;">
          <form id="feeDetailsForm" method="post" enctype="multipart/form-data" data-parsley-validate="">
          <?php if(is_array($branches) && count($branches) > 1){ ?>
          <div class="form-group">
          <label class="col-md-3 control-label" for="branch">Branch</label>  
          <div class="col-md-9">
            <select required="" class="form-control" name="branches" id="branches_id"style="margin-bottom:10px;">
              <option value="">Select Branch</option>
                <?php foreach ($branches as $branch) {?>
                    <option value="<?php echo $branch->id; ?>"><?php echo $branch->name; ?></option>
               <?php } ?>
            </select>
          </div>   
        </div>
        <?php } ?>
            <div class="form-group">
              <label class="col-md-3 control-label" for="feename"> Fee Type</label>  
              <div class="col-md-9">
                <input name="feename" id="fee_name_view" class="form-control" placeholder="Fee Blueprint name" type="text"/>
                <span class="help-block">Academic Fee, Etc...</span>
                <div class="checkbox">
                  <label><input type="checkbox" name="is_acad_fee" id="isacad_fee" value="1">Is this an academic fee</label>
                </div>
              </div>   
            </div>
            

            <div class="form-group">
              <label class="col-md-3 control-label" for="description"></label>  
              <div class="col-md-9">
                <input name="description" required="" id="description_fee" class="form-control" placeholder="Enter description" type="text"/>
                <span class="help-block">Fee description ex: Fee Academic 2019-20, etc..</span>
              </div>   
            </div>

            <div class="form-group">
              <label class="col-md-3 control-label" for="acad_year"></label>
              <div class="col-md-9">
                <select class="form-control" required="" id="acadyear" name="acad_year">
                    <option value="">Select Year</option>        
                  <?php foreach ($this->acad_year->getAllYearData() as $year) { ?>
                    <option value="<?php echo $year->id ?>"><?php echo $year->acad_year ?></option>
                  <?php } ?>
                </select>
                 <span class="help-block">Select Acad year blueprint</span>
              </div>
            </div>

            <div class="form-group">
              <label class="col-md-3 control-label" for="concession_mode"> Concession Algo</label>
              <div class="col-md-9">
                <select class="form-control" required="" id="concessionalgo" name="concession_algo">
                  <option value="none">None</option>
                  <option value="manual">Enter Manually</option>
                  <?php foreach ($this->config->item('concession_name') as $key => $name) { ?>
                    <option value="<?php echo $name ?>"><?php echo $name ?></option>
                  <?php } ?>
                </select>
                <span class="help-block">Select 'none' if there is no concession requirement. Select 'Percentage' if concession needs to be in percentage; 'Number' if concession needs to be in number.</span>
              </div>
            </div>

            <div class="form-group">
              <label class="col-md-3 control-label" for="concession_algo"> Concession Mode</label>
              <div class="col-md-9">
                <select class="form-control" required="" id="concessionmode" name="concession_mode">
                  <option value="none">None</option>
                  <option value="percentage">Enter Percentage</option>
                  <option value="amount">Enter Amount</option>
                </select>
                <span class="help-block">Select 'none' if there is no concession requirement. Select 'Percentage' if concession needs to be in percentage; 'Number' if concession needs to be in number.</span>
              </div>

              <div class="form-group">
                <div class="col-md-3">
            </div>


              <div class="col-md-9">
              <div class="checkbox">
                <label><input type="checkbox" name="is_admission_fees" id="admission_fee" value="1" >Is Admission Fee</label>
                <span class="help-block">Select this if the fee is for Admission.</span>
              </div>
              <div class="checkbox">
                <label><input type="checkbox" name="enable_parent_notification" id="enable_parent_notification" value="1" >Enabled Parent Nofication</label>
                <span class="help-block">Select this parent get notification when Publish fee and pay the fee payment online (Customization required use the config name="parent_fee_payment_notifcation").</span>
              </div>
                <div class="checkbox">
                  <label><input type="checkbox" name="enable_custom_fee" id="enablecustom_fee" value="1" >Enable Custom Fee</label>
                  <span class="help-block">Select this if you need the ability to provide a custom fee for any student for this Fee blueprint.</span>
                </div>
                <div class="checkbox">
                  <label><input type="checkbox" name="enable_fee_cohort_check" id="enable_feecohort_check"  value="1" >Enable Fee Cohort Check </label>
                  <span class="help-block">Select this if the cashier needs the ability to confirm the fee structure for the student before collecting the fee.</span>
                </div>
                <div class="checkbox">
                  <label><input type="checkbox" name="is_split" id="issplit" value="1">Split amount into multiple accounts? </label>
                  <span class="help-block">If enabled, the amount will be split into the accounts as  specified against the components. </span>
                </div>
                <div class="checkbox">
                  <label><input type="checkbox" name="enable_sms" id="enablesms" value="1">SMS /Notifcation for in counter pay the fees ? </label>
                  <span class="help-block">If enabled, The Notification/SMS will be sent to both parents. (Note: SMS only approved template) </span>
                </div>
              </div>

            <div class="form-group">
              <label class="col-md-3 control-label" for="receipt_for">Receipt for</label>
              <div class="col-md-9">
                <input type="text" name="receipt_for" id="receiptfor" class="form-control">
                <span class="help-block"></span>
              </div>
            </div>
            </div>
            
            </div>
            <center>
              <br>
              <button type="button" id="updatefeeDetails" onclick="update_fee_details()" class="btn btn-primary my-2">Update</button>
            </center>
          </form>
        </div>
        <!--End Bluprint Details Table-->
        

<!--Installment Type Table-->
        <div class="form-group" id="add_blueprint_installment" style="display:none;">
<h3 class="card-title panel_title_new_style_staff mb-0"><strong>Installment Type</strong></h3>

<form enctype="multipart/form-data" method="post" id="installment_type_form"  data-parsley-validate="" class="form-horizontal">

<input type="hidden" name="blueprintid_installmenttype" id="blueprintid_installmenttype">
<input type="hidden" name="installment_type_primary_id" id="installmenttypeprimaryId">

        <div class="form-group my-4">
          
         <label class="col-md-2 control-label" for="installment_type"> Installment Type</label>  
          <div class="col-md-7">
            <select class="form-control" id="installmenTypeId" name="feev2_installment_type_id">
              
              <?php 
                foreach ($instTypes as $instT) {
                  if ($instT->id == 1){
                    echo '<option value="' . $instT->id . '"  >' . $instT->name . ' </option>';
                  }
                else{
                  echo '<option value="' . $instT->id . '">' . $instT->name . '</option>';
                }
                 
              } ?>
            </select>
          </div>
        </div>

        <div class="form-group">
            <label class="col-md-2 control-label" for="staff_discount_amount_algo"> Staff Discount Algorithm</label>  
          <div class="col-md-7">
            <select class="form-control" name="staff_discount_amount_algo" id="staffdiscount_amount_algo" >
              <option value="" >None</option>
              <option value="manual_p">Manually Enter Percentage</option>
              <option value="manual_num">Manually Enter Amount</option>
              <option value="discount_if_full_paid_num">Discount based on percentage</option>
              <option value="discount_if_full_paid_p">Discount based on number</option>
            </select>
          </div>
        </div>

        <div class="form-group" id="staff_discountAmount" style="display: none">
            <label class="col-md-2 control-label" for="discount_amount_algo">Staff Discount</label>
          <div class="col-md-7"> 
            <input type="number" placeholder="Amount or Percentage" id="staff_discount_amountId" class="form-control" name="staff_discount_amountId">
          </div>
        </div>



<div class="form-group">
          <label class="col-md-2 control-label"  for="discount_amount_algo"> Discount Algorithm</label>  
          <div class="col-md-7">
            <select class="form-control" name="discount_amount_algo" id="discount_amount_algo" >
              <option value="" >None</option>
              <option value="manual_p">Manually Enter Percentage</option>
              <option value="manual_num">Manually Enter Amount</option>
              <option value="discount_if_full_paid_num">Discount if full paid number</option>
              <option value="discount_if_full_paid_p">Discount if full paid percentage</option>
            </select>
          </div>
        </div>

        <div class="form-group" id="discountAmount" style="display: none">
          <label class="col-md-2 control-label" for="discount_amount_algo">Discount</label>
          <div class="col-md-7"> 
            <input type="number" placeholder="Amount or Percentage" id="discount_amountId" class="form-control" name="discount_amountId">
          </div>
        </div>

        <div class="form-group">
          <label class="col-md-2 control-label" for="discount_amount_algo">Discount End Date</label>
          <div class="col-md-7"> 
            <input type="date" id="discount_end_date" class="form-control" name="discount_end_date">
          </div>
        </div>

        <div class="form-group checkbox">
          <div class="col-md-2"></div>
          <label><input type="checkbox" name="allow_partial" id="allow_partial" value="1">Allow Partial?</label>
        </div>
          

        <div class="form-group">
          <label class="col-md-2 control-label" for="allocation_algo">Allocation Algorithm</label>
          <div class="col-md-7">
            <select class="form-control" name="allocation_algo" id="allocation_algo">
              <option value="none" >None</option>
              <option value="equal">Equal across all installments</option>
              <option value="custom">Custom</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label class="col-md-2 control-label" for="algo_params">Algorithm Parameters</label>  
          <div class="col-md-7">
            <input name="algo_params" id="allocation_params" class="form-control" placeholder="Algorithm Params" type="text"/>
          </div>
        </div>

        <div class="form-group">
          <label class="col-md-2 control-label" for="fine_amount_algo">Fine Amount Alog</label>  
          <div class="col-md-7">
            <select class="form-control" name="fine_amount_algo" id="fine_amount_algo">
              <option value="none"  >None</option>
              <option value="manually_enter">Manually entry</option>
              <option value="fine_per_day">Fine per Day</option>
              <option value="fine_per_week">Fine per Week</option>
              <option value="fine_per_month">Fine per Month</option>
              <option value="fixed_fine">Fixed Fine</option>

              <option value="fine_json_bimonthly">Use JSON (Bimonthly)</option>
              <option value="fine_date_range">Date Range Fine</option>

            </select>
          </div>
        </div>

        <div class="form-group" id="fineAmount" style="display: none">
          <label class="col-md-2 control-label" for="algo_params">Fine Amount</label>
          <div class="col-md-7">
            <input name="fine_amount_params" id="fine_amount_params" class="form-control" placeholder="Fine amount Params" type="text"/>
          </div>
        </div>

        <div class="form-group" id="fineJsonHelp" style="display: none">
          <label class="col-md-2 control-label"></label>
          <div class="col-md-7">
             <div class="alert alert-info" style="margin-bottom:0;">
              <strong>JSON Format Examples:</strong><br>
              <b>Bimonthly:</b> <code>[{"bimonth":2,"fine":400}]</code><br>
              <b>Date Range:</b> <code>[{"start_date":"YYYY-MM-DD","end_date":"YYYY-MM-DD","fine":30},{"start_date":"YYYY-MM-DD","end_date":"YYYY-MM-DD","fine":60}]</code><br>
              </div>
          </div>
        </div>

          <center>
            <input type="button" class="btn btn-primary" id="installmenttypeButton" onclick="submit_installment_types_data()" value="Submit"/>
          </center>
      </form>
      
          <div class="col-md-12">
            <div class="col-md-12 my-4" id="displayinsttypes">
            </div>
        </div> 
        </div>

        <!--End Installment Type Table-->
          
<!--Fee Filters & Fields Table-->
        <div class="form-group" id="fee_all_filters" style="display:none;">
          <form id="filter_Form" method="post" enctype="multipart/form-data" data-parsley-validate="" action="<?php echo base_url('feesv2/fees_blueprint/save_filters'); ?>">
            <input type="hidden" name="blueprintid" id="blueprintid">
            <input type="hidden" name="datadisplayid" id="datadisplayid">
            <div class="col-md-12">
              <br>
              <h3 class="panel-title"><strong>Select Fee Filters <font color="red">*</font></strong></h3>
                <div class="col-md-6" id="displaycol1"></div>
                <div class="col-md-6" id="displaycol2"></div>
            </div>
            <div class="col-md-12">
              <br>
              <h3 class="panel-title"><strong>Select Fields to display in Fee Collection <font color="red">*</font></strong></h3>
              <div class="col-md-6" id="displayfeild1"></div>
              <div class="col-md-6" id="displayfeild2"> </div>
            </div>
            <div class="col-md-12">
              <center> <button type="button"  onclick="bluePrint_submitform()" class="btn btn-primary" href="javascript:void(0)">Save</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
              </center>
            </div> 
          </form>
        </div>
        <!--End Fee Filters & Fields Table-->

<!-- Selected Blueprints Table-->
        <div class="form-group" id="components_payments_mode" style="display:none;">
          <form id="install_components" method="post" enctype="multipart/form-data" data-parsley-validate="" action="<?php echo base_url('feesv2/fees_blueprint/save_payment_modes'); ?>">
            <input type="hidden" name="blueprintidpay" id="blueprintidpay">
            
            
            <div class="col-md-12" id="bluecomponents">

            </div>

            <div class="col-md-12" id="paymentmodes">

            </div>
            <div class="col-md-12 my-3"><center> <br><button id="datadisplay" name="SuBmit" type="submit"  class="btn btn-primary" >Add</button><button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button></center></div>
          </form>
        </div>

       <!-- End Selected Blueprints Table-->


        <!-- Adding Blueprints Popup-->

            
         
<!-- Adding Blueprints Popup-->

<div class="modal fade" id="subForm_add" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" data-backdrop="static" aria-hidden="true">
  <div class="modal-dialog" role="document" style="width:50%; margin-left: auto;margin-right:auto; margin-top:5%;">
    <div class="card cd_border">
      <div class="card-header panel_heading_new_style_staff_border">
        <div class="row m-0">
          <div class="col-md-9">
            <h3 class="card-title panel_title_new_style_staff mb-0"><strong>Add Blueprints</strong></h3>
          </div>
          <div class="col-md-3  d-flex justify-content-end">
            <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
          </div>
        </div>
      </div>
      <form id="subForm" method="post" enctype="multipart/form-data" data-parsley-validate="" action="<?php echo base_url('feesv2/fees_blueprint/save_blueprints'); ?>">
        <div class="card-body mx-3">
        <?php if(is_array($branches) && count($branches) > 1){ ?>
        <div class="form-group">
          <label class="col-md-3 control-label" for="branch">Branch</label>  
          <div class="col-md-9">
            <select required="" class="form-control" name="branches" id="branches"style="margin-bottom:10px;">
                            		<option value="">Select Branch</option>
                               
                            		 <?php foreach ($branches as $branch) {?>
                                      <option value="<?php echo $branch->id; ?>"><?php echo $branch->name; ?></option>
                                 <?php } ?>
                            	</select>
          </div>   
        </div>
        <?php } ?>

        

          <div class="form-group" style="">
          <label class="col-md-3 control-label" for="fee_name">Fee Type</label>  
          <div class="col-md-9">
            <input name="fee_name" required="" id="fee_name" class="form-control" placeholder="Fee Blueprint name" type="text"/>
            <span class="help-block">Academic Fee, Etc...</span>
            <div class="checkbox">
              <label><input type="checkbox" name="is_acad_fee" value="1">Is this an academic fee</label>
            </div>
          </div>   
        </div>

        <div class="form-group">
          <label class="col-md-3 control-label" for="description">Description</label>  
          <div class="col-md-9">
            <input name="description" required="" id="description" class="form-control" placeholder="Enter description" type="text"/>
            <span class="help-block">Fee description ex: Fee Academic 2019-20, etc..</span>
          </div>   
        </div>

         <div class="form-group">
          <label class="col-md-3 control-label" for="acad_year"></label>
          <div class="col-md-9">
            <select class="form-control" required=""  name="acad_year">
                <option value="">Select Year</option>        
              <?php foreach ($this->acad_year->getAllYearData() as $year) { ?>
                <option value="<?php echo $year->id ?>"><?php echo $year->acad_year ?></option>
              <?php } ?>
            </select>
             <span class="help-block">Select Acad year blueprint</span>
          </div>
        </div>
        <div class="form-group">
          <label class="col-md-3 control-label" for="concession_mode"> Concession Algo</label>
          <div class="col-md-9">
            <select class="form-control" required="" name="concession_algo">
              <option value="none">None</option>
              <option value="manual">Enter Manually</option>
              <?php foreach ($this->config->item('concession_name') as $key => $name) { ?>
                <option value="<?php echo $name ?>"><?php echo $name ?></option>
              <?php } ?>
            </select>
            <span class="help-block">Select 'none' if there is no concession requirement. Select 'Percentage' if concession needs to be in percentage; 'Number' if concession needs to be in number.</span>
          </div>
        </div>

        <div class="form-group">
          <label class="col-md-3 control-label" for="concession_algo"> Concession Mode</label>
          <div class="col-md-9">
            <select class="form-control" required="" name="concession_mode">
              <option value="none">None</option>
              <option value="percentage">Enter Percentage</option>
              <option value="amount">Enter Amount</option>
            </select>
            <span class="help-block">Select 'none' if there is no concession requirement. Select 'Percentage' if concession needs to be in percentage; 'Number' if concession needs to be in number.</span>
          </div>

          <div class="form-group">
            <div class="col-md-3">
        </div>


          <div class="col-md-9">
          <div class="checkbox">
              <label><input type="checkbox" name="is_admission_fees" value="1" >Is Admission Fee</label>
              <span class="help-block">Select this if the fee is for Admission.</span>
            </div>
            <div class="checkbox">
              <label><input type="checkbox" name="enable_custom_fee" value="1" checked>Enable Custom Fee</label>
              <span class="help-block">Select this if you need the ability to provide a custom fee for any student for this Fee blueprint.</span>
            </div>
            <div class="checkbox">
              <label><input type="checkbox" name="enable_fee_cohort_check" value="1" >Enable Fee Cohort Check </label>
              <span class="help-block">Select this if the cashier needs the ability to confirm the fee structure for the student before collecting the fee.</span>
            </div>
            <div class="checkbox">
              <label><input type="checkbox" name="is_split" value="1">Split amount into multiple accounts? </label>
              <span class="help-block">If enabled, the amount will be split into the accounts as  specified against the components. </span>
            </div>
            <div class="checkbox">
              <label><input type="checkbox" name="enable_sms" value="1">SMS for in counter pay the fees ? </label>
              <span class="help-block">If enabled, the sms will be sent to both parents. </span>
            </div>
          </div>

        <div class="form-group">
          <label class="col-md-3 control-label" for="receipt_for">Receipt for</label>
          <div class="col-md-9">
            <input type="text" name="receipt_for" required="" class="form-control">
            <span class="help-block"></span>
          </div>
        </div>
        </div>

        </div>

        </div>
        <div class="card-footer panel_footer_new">
          <center> <button id="submitBtn" name="SuBmit" type="submit" class="btn btn-primary" 
          >Add</button>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </center>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- End Adding Blueprints Popup-->

<!-- Selected Installment Type -->
<!-- <div class="form-group" id="fine_discount" style="display:none;">
  <h3 class="card-title panel_title_new_style_staff mb-0"><strong>Add Fine and Discount</strong></h3>
              <form enctype="multipart/form-data" method="post" id="demo-form" action="<?php echo site_url('feesv2/fees_blueprint/update_fine_discount_alogo_new');?>" data-parsley-validate="" class="form-horizontal">

            <input type="hidden" name="blueprintid_fine_discount" id="blueprintid_fine_discount">


            <div class="panel-body">
            <div class="form-group" id="staff_discountAmount" style="display: none">
                <label class="col-md-2 control-label" for="discount_amount_algo_fine">Discount</label>
              <div class="col-md-7"> 
                <input type="number" placeholder="Amount or Percentage" id="discount_amountId" class="form-control" name="discount_amount">
              </div>
            </div>

            <div class="form-group">
              <label class="col-md-2 control-label" for="discount_amount_algo_fine"> Discount Algorithm</label>  
              <div class="col-md-7">
                <select class="form-control" name="discount_amount_algo_fine" id="discount_amount_algo_fine" >
                  <option value="">None</option>
                  <option value="manual_p">Manually Enter Percentage</option>
                  <option value="manual_num">Manually Enter Amount</option>
                  <option value="discount_if_full_paid_num">Discount if full paid number</option>
                  <option value="discount_if_full_paid_p">Discount if full paid percentage</option>
                  <option value="discount_if_full_paid_json">Discount in JSON Format</option>
                </select>
              </div>
            </div>


            <div class="form-group" id="discountAmount_fine" style="display: none">
                <label class="col-md-2 control-label" for="discount_amountId">Discount</label>
              <div class="col-md-7"> 
                <input type="number" placeholder="Amount or Percentage" id="discount_amountId_fine" class="form-control" name="discount_amount">
              </div>
            </div>

            <div class="form-group">
              <label class="col-md-2 control-label" for="allocation_algo">Allocation Algorithm</label>
              <div class="col-md-7">
                <select class="form-control" name="allocation_algo">
                  <option value="">None</option>
                  <option value="equal">Equal across all installments</option>
                  <option value="custom">Custom</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="col-md-2 control-label" for="algo_params">Algorithm Parameters</label>  
              <div class="col-md-7">
                <input name="algo_params" id="allocation_params" class="form-control" placeholder="Algorithm Params" type="text"/>
              </div>
            </div>

            <div class="form-group">
              <label class="col-md-2 control-label" for="fine_amount_algo_fine"> Fine Amount Alog</label>  
              <div class="col-md-7">
                <select class="form-control" name="fine_amount_algo_fine" id="fine_amount_algo_fine">
                  <option value="">None</option>
                  <option value="manually_enter">Manually entry</option>
                  <option value="fine_per_day">Fine per Day</option>
                  <option value="fine_per_week">Fine per week</option>
                  <option value="fine_per_month">Fine per month</option>
                  <option value="fixed_fine">Fixed Fine</option>
                  <option value="fine_json_day">Use JSON (Daily)</option>
                  <option value="fine_json_week">Use JSON (Weekly)</option>
                  <option value="fine_json_month">Use JSON (Monthly)</option>
                  <option value="fine_json_bimonthly">Use JSON (Bimonthly)</option>
                  <option value="fine_date_range">Date Range Fine</option>
                  <option value="fine_cumulative_range">Cumulative Range Fine</option>
                </select>
              </div>
            </div>

            <div class="form-group" id="fineAmount_fine" style="display: none">
              <label class="col-md-2 control-label" for="algo_params">Fine Amount</label>  
              <div class="col-md-7">
                <input name="fine_amount_params_fine" id="fine_amount_params_fine" class="form-control" placeholder="Fine amount Params" type="text"/>
              </div>
            </div>                                   
            </div>

            <center>
              <input type="submit" class="btn btn-primary" value="Submit"/>
              <button type="button" class="btn btn-warning" data-dismiss="modal">Cancel</button>
            </center>

            </form>
        </div> -->
        <!-- End Selected Installment Type -->



<script type="text/javascript">
  $(document).ready(function() {
    
  get_blueprints();
});

function update_fee_details(){
 var blueprint_id = $('#blueprintidpay').val();
 var $form = $('#feeDetailsForm');
  if ($form.parsley().validate()){
    var form = $('#feeDetailsForm')[0];
    var formData = new FormData(form);
    formData.append('blueprint_id',blueprint_id);
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_blueprint/update_fee_blueprints'); ?>',
      type: 'post',
      data: formData,
      // async: false,
      processData: false,
      contentType: false,
      // cache : false,
      success: function(data) {
        if(data){
          $(function(){
            new PNotify({
              title: 'Success',
              text:  'Successfully updated',
              type: 'success',
            });
          });
        }else{
          $(function(){
            new PNotify({
              title: 'Error',
              text:  'Something went wrong..',
              type: 'err',
            });
          });
        }
        get_blueprintId(blueprint_id);
      }
    });
  }
}

function submit_installment_types_data(){
  var blueprint_id = $('#blueprintidpay').val();
 var $form = $('#feeDetailsForm');
  if ($form.parsley().validate()){
    var form = $('#installment_type_form')[0];
    var formData = new FormData(form);
    formData.append('blueprint_id',blueprint_id);
    if ($('#allow_partial').is(':checked')) {
      formData.append('allow_partial', '1');
    } else {
      formData.append('allow_partial', '0');
    }
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_blueprint/submit_fee_blueprint_instament_type_new'); ?>',
      type: 'post',
      data: formData,
      // async: false,
      processData: false,
      contentType: false,
      // cache : false,
      success: function(data) {
        //console.log(data);
        $('#installmenttypeButton').val('Submit');
        $('#installmenttypeprimaryId').val('');
        if(data){
          $(function(){
            new PNotify({
              title: 'Success',
              text:  'Successfully inserted',
              type: 'success',
            });
          });
        }else{
          $(function(){
            new PNotify({
              title: 'Error',
              text:  'Something went wrong..',
              type: 'err',
            });
          });
        }
        // get_blueprints();
        get_blueprintId(blueprint_id);
      }
    });
  }
}




function insrowpayment(){
    var x=document.getElementById('paymode');
    var new_rowp = x.rows[1].cloneNode(true);
    var plen = x.rows.length;
    new_rowp.cells[0].innerHTML = plen;
    var pinp = new_rowp.cells[1].getElementsByTagName('select')[0];
    pinp.id += plen;
    pinp.value += '';
    var pyinp = new_rowp.cells[2].getElementsByTagName('select')[0];
    pyinp.id += plen;
    pyinp.value += '';
    x.appendChild(new_rowp ); 
}

function deleterowpayment(row){
    var i=row.parentNode.parentNode.rowIndex;
    if(i==1){
      alert('you can not delete');
    }else{
     document.getElementById('paymode').deleteRow(i);
    } 
}

function get_blueprints(){
  $('#buttonsvisibility').hide();
  
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_blueprint/get_blueprints'); ?>',
    type: 'post',
    success: function(data) {
      
      parsed_data = $.parseJSON(data);
      var blueprint = parsed_data;
      $('#buttonsvisibility').show();
      $('.loaderclass').css('display','none');
      
      //console.log(blueprint);
      html = construct_blueprint_data(blueprint);
      
      
      $("#capacity_table").html(html);
      
      if (blueprint.length > 0) {
          get_blueprintId(blueprint[0].id);
      }
      
    },
    error: function (err) {
      console.log(err);
    }
  });
  
}

function construct_blueprint_data(blueprint) {
  var ac = 1 ;
  var output='';
  $('#dataTable').find("option:active").val()
  output += '<table class="table table-bordered datatable " id="dataTable">';
  output +='<thead>';
  output += '<tr>';
  output += '<th>Blueprints</th>';
  output += '</tr>';
  output +='</thead>';
  output +='<tbody>';

  for(var i = 0; i < blueprint.length; i++){
    if (ac == 1 ){
      var active = 'active';
    }else{
      var active = '';
    }
    var receipt_book = blueprint[i].infix + '' + blueprint[i].year + '' + blueprint[i].running_number;
      output += '<tr>';
      output += '<td ><a id="'+blueprint[i].id+'" href="javascript:void(0)" onclick="get_blueprintId('+blueprint[i].id+')" class="list-group-item '+active+' " ><span style="color: 000; font-weight: bold;"> '+blueprint[i].blueprint_name+' </span><br><b>Receipt For: </b> '+blueprint[i].receipt_for+'<br> <b>Receipt No :</b>'+receipt_book+'</a></td>';
      output += '</tr>';
    ac++;
    }
  output +='</tbody>';
  output += '</table>';

  return output;
}
function selectRow(element) {

  var rows = document.querySelectorAll('#dataTable tbody tr');
  rows.forEach(function(row) {
    row.classList.remove('active');
  });

  var selectedRow = element.closest('tr');
  selectedRow.classList.add('active');
}

function get_blueprintId(id) {
  
  $('.list-group-item').removeClass('active');
  $('#'+id).addClass('active');
  $('#blueprintidpay').val(id);
  $('#blueprintid_receipt_html').val(id);
  $('#blueprintid_terms_condition').val(id);
  $('#blueprintid_fine_discount').val(id);
  $('#blueprintid').val(id);
  $('#blueprintid_installmenttype').val(id);
  $('#fee_blueprint_details').show();
  $('#fee_all_filters').hide();
  $('#components_payments_mode').hide();
  $('#add_blueprint_installment').hide();
  
   $.ajax({
    url: '<?php echo site_url('feesv2/fees_blueprint/get_blueprintId'); ?>',
    type: 'post',
    data: {'id':id} ,
    success: function(data) {
      var resData = $.parseJSON(data);
      console.log(resData);

          $('#fee_name_view').val(resData.name);
          

          if(resData.is_acad_fee == 1){
          $('#isacad_fee').prop('checked','checked');
            }else{
              $('#isacad_fee').prop('checked',false);
            }

          $('#description_fee').val(resData.description);
          $('#acadyear').val(resData.acad_year_id);
          $('#branches_id option[value="'+resData.branches+'"]').prop('selected',true);
          $('#concessionalgo').val(resData.concession_algo);
          $('#concessionmode').val(resData.concession_mode);

          if(resData.enable_custom_fee == 1){
          $('#enablecustom_fee').prop('checked','checked');
            }else{
              $('#enablecustom_fee').prop('checked',false);
            }
            if(resData.is_admission_fees == 1){
          $('#admission_fee').prop('checked','checked');
            }else{
              $('#admission_fee').prop('checked',false);
            }

            if(resData.enable_fee_cohort_check == 1){
          $('#enable_feecohort_check').prop('checked','checked');
            }else{
              $('#enable_feecohort_check').prop('checked',false);
            }

            if(resData.is_split == 1){
          $('#issplit').prop('checked','checked');
            }else{
              $('#issplit').prop('checked',false);
            }

            if(resData.enable_sms == 1){
          $('#enablesms').prop('checked','checked');
            }else{
              $('#enablesms').prop('checked',false);
            }

          $('#receiptfor').val(resData.receipt_for);

      }
    })
}


function fee_all_filters() {
  $('#fee_blueprint_details').hide();
  $('#fee_all_filters').show();
  $('#add_blueprint_installment').hide();
  $('#components_payments_mode').hide();
  get_blueprint_details_by_id();
}


function add_blueprint_installment() {
  $('#fee_blueprint_details').hide();
  $('#add_blueprint_installment').show();
  $('#components_payments_mode').hide();
  $('#fee_all_filters').hide();
  get_blueprint_details_by_id();
}

function payments_mode() {
  $('#fee_blueprint_details').hide();
  $('#fee_all_filters').hide();
  $('#add_blueprint_installment').hide();
  $('#components_payments_mode').show();
  get_blueprint_details_by_id();
}


function show_instacomp_data(payment_modes, blueprint_components,accounts){
  //console.log(blueprint_components);
 
  var html ='';
  var html1 ='';
  html +='<h2>Fees Blue Print Components<font color="red">*</font></h2>';
  html +='<ul class="panel-controls"><li><a href="javascript:void(0)" data-placement="top" data-toggle="tooltip" data-original-title="Add Row" onclick="insRow()" id="addmorePOIbutton"  class="control-primary"><span class="fa fa-plus"></span></a></li></ul>';
  html +='<div class="form-group"><div class="col-md-12"><div id="POItablediv"><table id="POITable" >';
  html +='<thead><tr>';
  html +='<th style="display:none">#</th><th>Component</th><th>Accounts</th><th >Concession Eligible?</th><th style="padding: 5px;">Enable for partial Fee?</th></tr></thead>';
  html +='<tbody><tr>';
 if(blueprint_components !=""){
  for (var k in blueprint_components){
    //console.log(blueprint_components[k].vendor_code);
        html +='<td style="display:none">1</td>';
        html +='<td ><input name="blueprint_comp['+blueprint_components[k].id+']" required="" class="form-control" placeholder="Component Name" type="text"/ value="'+blueprint_components[k].name+'"></td>';
        html +='<td><select class="form-control" id="accountId" name="accounts['+blueprint_components[k].id+']"><option value="0">Select Accounts</option>';
        for (var i = 0; i < accounts.length; i++) {
          var seletedvalues = accounts[i].tracknpay_vendor_id
          var selected = "";
          if (blueprint_components[k].vendor_code == accounts[i].tracknpay_vendor_id){
            selected = "selected";
          }
          html += '<option value="' + accounts[i].tracknpay_vendor_id + '" ' + selected + ' > ' + accounts[i].name + ' ('+accounts[i].account+')</option>';
      }
        html +='</select></td>';
        var selectConcession = "";
        var selectPartial = "";
        if (blueprint_components[k].is_concession_eligible == 0){
          selectConcession = "";
        } else {
          selectConcession = "selected";
        } 
        if (blueprint_components[k].enable_if_partial == 0){ 
          selectPartial = "";
        } else {
          selectPartial = "selected";
        }
        html += '<td><select class="form-control" name="is_conc_eligible['+blueprint_components[k].id+']">';
        html += '<option value="0" '+selectConcession+'>No</option>';
        html += '<option value="1" '+selectConcession+'>Yes</option>';
        html += '</select></td>';

        html += '<td><select class="form-control" name="enable_if_partial['+blueprint_components[k].id+']">';
        html += '<option value="0" '+selectPartial+'>No</option>';
        html += '<option value="1" '+selectPartial+'>Yes</option>';
        html += '</select></td>';

        html +='<td><ul class="panel-controls" id="delPOIbutton" onclick="deleteRow(this)"><li><a href="javascript:void(0)" style="border-color: #ff0000;" class="control-primary"><span style=" color:red" class="glyphicon glyphicon-remove"></span></a></li></ul></td>';
        html +='</tr></tbody>';
        
        
  }
}else{
  html +='<td style="display:none">1</td>';
        html +='<td ><input name="blueprint_comp[]" required="" class="form-control" placeholder="Component Name" type="text"/ value=""></td>';
        html +='<td><select class="form-control" id="accountId" name="accounts[]"><option value="0">Select Accounts</option>';
        for (var i = 0; i < accounts.length; i++) {
          html += '<option value="' + accounts[i].tracknpay_vendor_id + '" ' + selected + ' > ' + accounts[i].name + ' ('+accounts[i].account+')</option>';
      }
        html +='</select></td>';

       

        html += '<td><select class="form-control" name="is_conc_eligible[]">';
        html += '<option value="0" >No</option>';
        html += '<option value="1">Yes</option>';
        html += '</select></td>';

        html += '<td><select class="form-control" name="enable_if_partial[]">';
        html += '<option value="0" >No</option>';
        html += '<option value="1" >Yes</option>';
        html += '</select></td>';

        html +='<td><ul class="panel-controls" id="delPOIbutton" onclick="deleteRow(this)"><li><a href="javascript:void(0)" style="border-color: #ff0000;" class="control-primary"><span style=" color:red" class="glyphicon glyphicon-remove"></span></a></li></ul></td>';
        html +='</tr></tbody>';
}
  var defaultPayments = [{value:'credit_card__2',name:'Credit Card'},{value:'dd__1',name:'DD'},{value:'debit_card__3',name:'Debit Card'},{value:'cheque__4',name:'Cheque'},{value:'wallet_payment__5',name:'Wallet Payment'},{value:'challan__6',name:'Challan'},{value:'card__7',name:'Card (POS)'},{value:'net_banking__8',name:'Net Banking'},{value:'cash__9',name:'Cash'},{value:'online_link__10',name:'Online Link'},{value:'upi__11',name:'UPI'},{value:'loan__12',name:'Loan Provider Charges'}];


  html1 +='<h3 class="panel-title my-3"><strong>Payments Mode <font color="red">*</font></strong></h3>';
  html1 +='<ul class="panel-controls">';
  html1 +=' <li><a href="javascript:void(0)" data-placement="top" data-toggle="tooltip" data-original-title="Add Row" onclick="insrowpayment()"   class="control-primary"><span class="fa fa-plus"></span></a></li>';
  html1 +='</ul>';
  html1 +='<div class="form-group"><div class="col-md-12"><div id="POItablediv">';
  html1 +='<table id="paymode" ><thead><tr>';
  html1 +='<td style="display: none;">#</td><th>Name</th><th>Reconcilation Require</th></tr></thead>';

   if(payment_modes != '' && payment_modes != null){
            for (var i in payment_modes){
              var SelectePaymentValues = payment_modes[i].name+'__'+payment_modes[i].value;
              html1 +='<tbody><tr>';
              html1 +='<td style="display: none;">1</td>';
              html1 +='<td><select required="" class="form-control" id="allowed_payment_modes" name="allowed_payment_modes[]">';
              html1 +='<option value="" >Select Payment Modes</option>';
              for (let index = 0; index < defaultPayments.length; index++) {
                var selected = '';
                if(SelectePaymentValues == defaultPayments[index].value){
                  selected ='selected';
                }
                html1 +='<option '+selected+' value="'+defaultPayments[index].value+'">'+defaultPayments[index].name+'</option>';
              }
              html1 +='</select></td>';
              html1 +='<td align="center" style="padding: 5px;">';
              html1 +='<select class="form-control" name="reconcilation_reqd[]">';
              var reconNo ='';
              if(payment_modes[i].reconcilation_reqd == 0){
                reconNo ='selected';
              }
              var reconYes ='';
              if(payment_modes[i].reconcilation_reqd == 1){
                reconYes ='selected';
              }
              html1 +='<option '+reconNo+'  value="0">No</option><option '+reconYes+'  value="1">Yes</option></select>';
              html1 +='</td>';
              html1 +='<td>';
              html1 +='<ul class="panel-controls" id="delPOIbutton" onclick="deleterowpayment(this)" >';
              html1 +='<li><a href="javascript:void(0)" style="border-color: #ff0000;" class="control-primary"><span style=" color:red" class="glyphicon glyphicon-remove"></span></a></li>';
              html1 +='</ul></td></tr>';
              
            }
          }else{
            html1 +='<tbody><tr>';
              html1 +='<td style="display: none;">1</td>';
              html1 +='<td><select required="" class="form-control" id="allowed_payment_modes" name="allowed_payment_modes[]">';
              html1 +='<option value="" >Select Payment Modes</option>';
              for (let index = 0; index < defaultPayments.length; index++) {
                
                html1 +='<option  value="'+defaultPayments[index].value+'">'+defaultPayments[index].name+'</option>';
              }
              html1 +='</select></td>';
              html1 +='<td align="center" style="padding: 5px;">';
              html1 +='<select class="form-control" name="reconcilation_reqd[]">';
            
              html1 +='<option value="0">No</option><option value="1">Yes</option></select>';
              html1 +='</td>';
              html1 +='<td>';
              html1 +='<ul class="panel-controls" id="delPOIbutton" onclick="deleterowpayment(this)" >';
              html1 +='<li><a href="javascript:void(0)" style="border-color: #ff0000;" class="control-primary"><span style=" color:red" class="glyphicon glyphicon-remove"></span></a></li>';
              html1 +='</ul></td></tr>';

          }
  html1 +='</tbody></table></div></div></div>';
  
  

        $('#bluecomponents').html(html);
        $('#paymentmodes').html(html1);

}

function fee_blueprint_details() {
  $('#fee_blueprint_details').show();
  $('#fee_all_filters').hide();
  $('#components_payments_mode').hide();
  $('#add_blueprint_installment').hide();
  $('#fine_discount').hide();
  
}

function get_blueprint_details_by_id(){
  $('#displayinsttypes').html('');
  
  var blueprintId = $('#blueprintid').val();
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_blueprint/get_blueprints_details'); ?>',
    type: 'post',
    data:{'blueprintId':blueprintId},
    success: function(data) {
      var parsed_data = $.parseJSON(data);
     
      var filter = parsed_data.filter;
      var columns = parsed_data.columns;
      var blueprint_installments = parsed_data.blueprint_installments;
      //console.log(blueprint_installments);
      var payment_modes = parsed_data.payment_modes;
      var blueprint_components = parsed_data.blueprint_components;
      var accounts = parsed_data.accounts;
    
      //console.log(payment_modes);
      //console.log(blueprint_components);
      show_fees_blueprint_filter_data(filter);
      show_fees_blueprint_feilds_data(columns);
      show_bluePrint_data(blueprint_installments);
      show_instacomp_data(payment_modes, blueprint_components,accounts);
      
      
      
    },
    error: function (err) {
        console.log(err);
    }
  });
}

function edit_instalType(bpinsId){
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_blueprint/get_blueprints_installmentstype_data'); ?>',
    type: 'post',
    data:{'bpinsId':bpinsId},
    success: function(data) {
      var parsed_data = $.parseJSON(data);
      console.log(parsed_data);
      $('#installmenttypeButton').val('Update');
      $('#installmenttypeprimaryId').val(parsed_data.id);
      $('#installmenTypeId').val(parsed_data.feev2_installment_type_id);
      $('#staffdiscount_amount_algo').val(parsed_data.staff_discount_amount_algo);
      $('#staff_discount_amountId').val(parsed_data.staff_discount_amount);
      $('#discount_amount_algo').val(parsed_data.discount_algo);
      $('#discount_amountId').val(parsed_data.discount_amount);
      $('#discount_end_date').val(parsed_data.enabled_discount_end_date);
      if (parsed_data.allow_partial == '1'){
        $('#allow_partial').prop('checked', true);
      }else{
        $('#allow_partial').prop('checked', false);

      }
      
      $('#allocation_algo').val(parsed_data.allocation_algo);
      $('#allocation_params').val(parsed_data.allocation_params);
      $('#fine_amount_algo').val(parsed_data.fine_amount_algo);
      $('#fine_amount_params').val(parsed_data.fine_amount_params);
     
      var algo = parsed_data.staff_discount_amount_algo;
      if (algo == 'discount_if_full_paid_num' || algo == 'discount_if_full_paid_p' ) {
        $('#staff_discountAmount').show();
      }else{
        $('#staff_discountAmount').hide();
        //$('#staff_discount_amountId').val();
      }
      var algo1 = parsed_data.discount_algo;
      if (algo1 == 'discount_if_full_paid_num' || algo1 == 'discount_if_full_paid_p') {
        $('#discountAmount').show();
      }else{
        $('#discountAmount').hide();
        //$('#discount_amountId').val();
      }
      var algo2 = parsed_data.fine_amount_algo;
      if (algo2 == 'fine_per_day') {
        $('#fineAmount').show();
      }else{
        $('#fineAmount').hide();
      }
      
    },
    error: function (err) {
        console.log(err);
    }
  });
    
}

function show_bluePrint_data(bpIns){
  //console.log(bpIns);
            var html='';
            if (bpIns.length >0 ){
            html +='<div class="container ">';
            html +='<h3 class="panel-title panel_title_new_style_staff "><strong>Selected Installment Type </strong></h3>';
            html +='<table class="table table-bordered datatable my-4 " id="ins_type" > ';
            html +='<tr><td>Installment Type</td><td>Staff Discount Algorithm</td><td>Discount Algorithm</td><td>Allocation Algorithm</td><td>Fine Amount</td><td>Discount End Date</td><td>Action</td></tr>';
            for(var k in bpIns){
              var staff_discount_amount_algo = '';
              if (bpIns[k].staff_discount_amount_algo == "none"){
                staff_discount_amount_algo = '-';
              }else{
                staff_discount_amount_algo= bpIns[k].staff_discount_amount_algo;
              }
              var allocation_algo = '';
              if (bpIns[k].allocation_algo == ""){
                allocation_algo = '-';
              }else{
                allocation_algo = bpIns[k].allocation_algo ;

              }
            html +='<tr><td>'+bpIns[k].name+'</td><td>'+staff_discount_amount_algo+'</td><td>'+bpIns[k].discount_algo+'</td><td>'+allocation_algo+'</td><td>'+bpIns[k].fine_amount_params+'</td><td>'+bpIns[k].enabled_discount_end_date+'</td><td><a onclick="edit_instalType('+bpIns[k].id+')" href="javascript:void(0)"><i class="fa fa-pencil"></i></a></td></tr>';
            }
            html +='</table>';
                        
            html +='</div>';
            $('#displayinsttypes').html(html);
    } 
    else {
        $('#displayinsttypes').html('<h3>No Data Found</h3>');
    }
  }


  

function show_fees_blueprint_filter_data(filter){
 var colmn1 = {
              admission_type:'Admission type',
              medium:'Medium',
              is_rte:'RTE',
              category:'Category',
              academic_year_of_joining:'Academic year of joiningy',
              category:'Category',
              class:'Class',
              board:'Board',
              gender:'Gender',
              physical_disability:'Physical Disability',
              is_lifetime_student:'Is lifetime student',
              quota:'Quota',
            };
            var colmn2 = {
              attempt:'Attempt',
              boarding:'Boarding',
              routes:'Routes',
              stop:'Stops',
              pickup_mode:'Pickup mode',
              has_staff:"Staff's kid",
              has_sibling:"Sibling",
              class_type:"Class Type",
              has_transport:"Transport",
              has_transport_km:"KM",
              combination:"Combination",
          };
         

  var html = '';
  for(var k in colmn1){
    var checked = '';
    if(jQuery.inArray(k, filter) !== -1){
      checked = 'checked';
    }
     
    html +='<div class="checkbox">';
    html +='<label><input type="checkbox" '+checked+' name="filter_blueprint[]" value="'+k+'">'+colmn1[k]+'</label>';
    html +='</div>';
  }
  
  var htmlF1 = '';
  for(var k1 in colmn2){
    var checked1 = '';
    if(jQuery.inArray(k1, filter) !== -1){
      checked = 'checked';
    }
       
    htmlF1 +='<div class="checkbox">';
    htmlF1 +='<label><input type="checkbox" '+checked+' name="filter_blueprint[]" value="'+k1+'">'+colmn2[k1]+'</label>';
    htmlF1 +='</div>';
  }
  $('#displaycol1').html(html);
  $('#displaycol2').html(htmlF1);
  
  
} 

    

function show_fees_blueprint_feilds_data(columns){
 var feilds1 = {
              admission_type:'Admission type',
              medium:'Medium',
              rte:'RTE',
              category:'Category',
              academic_year_of_joining:'Academic year of joiningy',
              category:'Category',
              class:'Class',
              is_rte:'RTE',
              has_transport:'Transport',
              gender:'Gender',
              physical_disability:'Physical Disability',
              father_name:'Father Name',
              father_phone:'Father Phone',
              father_email:'Father Email',
            };
  var feilds2 = {
              attempt:'Attempt',
              boarding:'Boarding',
              routes:'Routes',
              stop:'Stops',
              pickup_mode:'Pickup mode',
              has_staff:"Staff's kid",
              has_sibling:"Sibling",
              class_type:"Class Type",
              has_transport_km:"KM",
              is_lifetime_student:"Is lifetime student",
              combination:"Combination",
              mother_name:"Mother Name",
              mother_phone:"Mother Phone",
              mother_email:"Mother Email",
          };

  var colhtml = '';
  for(var k in feilds1){
    var checked = '';
    if(jQuery.inArray(k, columns) !== -1){
      checked = 'checked';
    }
    colhtml +='<div class="checkbox">';
    colhtml +='<label><input type="checkbox" '+checked+' name="filds_blueprint[]" value="'+k+'">'+feilds1[k]+'</label>';
    colhtml +='</div>';
  }
  var colhtml2 = '';
  for(var k1 in feilds2){
    var checked = '';
    if(jQuery.inArray(k1, columns) !== -1){
      checked = 'checked';
    }
    colhtml2 +='<div class="checkbox">';
    colhtml2 +='<label><input type="checkbox" '+checked+' name="filds_blueprint[]" value="'+k1+'">'+feilds2[k1]+'</label>';
    colhtml2 +='</div>';
  }
  $('#displayfeild1').html(colhtml);
  $('#displayfeild2').html(colhtml2);
  
  
}

function bluePrint_submitform() {
    
        //console.log('saved');
  var blueprint_id = $('#blueprintid').val();
 var $form = $('#filter_Form');
  if ($form.parsley().validate()){
    var form = $('#filter_Form')[0];
    var formData = new FormData(form);
    formData.append('blueprint_id',blueprint_id);
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_blueprint/save_filters'); ?>',
      type: 'post',
      data: formData,
      
      
      // async: false,
      processData: false,
      contentType: false,
      // cache : false,
      success: function(data) {
       
        if(data){
          $(function(){
            
            new PNotify({
              title: 'Success',
              text:  'Successfully updated',
              type: 'success',
            });
          });
        }else{
          $(function(){
            new PNotify({
              title: 'Error',
              text:  'Something went wrong..',
              type: 'err',
            });
          });
        }
        //get_blueprints();
        get_blueprintId(blueprint_id);
       
      }
    });
  }
  }

  


function deleteRow(row){
  var i=row.parentNode.parentNode.rowIndex;
  if(i==1){
    alert('you can not delete');
  }else{
   document.getElementById('POITable').deleteRow(i);
  } 
}

function insRow(){
  var x=document.getElementById('POITable');
  var new_row = x.rows[1].cloneNode(true);
  var len = x.rows.length;
  new_row.cells[0].innerHTML = len;
  var inp1 = new_row.cells[1].getElementsByTagName('input')[0];
  inp1.id += len;
  inp1.value = '';
  inp1.name = 'blueprint_comp[0]';

  var inp2 = new_row.cells[2].getElementsByTagName('select')[0];
  inp2.id += len;
  inp2.name = 'accounts[0]';

  var inp3 = new_row.cells[3].getElementsByTagName('select')[0];
  inp3.id += len;
  inp3.name = 'is_conc_eligible[0]';

  var inp4 = new_row.cells[4].getElementsByTagName('select')[0];
  inp4.id += len;
  inp4.name = 'enable_if_partial[0]';
  // console.log(inp1);
  // console.log(inp2);
  // console.log(inp3);
  // console.log(inp4);
  x.appendChild(new_row ); 
}



function add_receipt_book() {
  $('#add_receipt_book').modal('show');
  insert_receipts_book()
  //console.log($fee_receipt_book);
}
function full_fee_html_recepit() {
  $('#full_fee_html_recepit').modal('show');
  
}


function html_recepit() {
  $('#html_recepit').modal('show');
  receipt_html_display();
}

function receipt_html_display() {
    $("#full_receipt_html").val('');
    var blueprintId = $('#blueprintid_receipt_html').val();
    $.ajax({
    url: '<?php echo site_url('feesv2/fees_blueprint/receipt_html_display'); ?>',
    type: 'post',
    data:{'blueprintId':blueprintId},
    success: function(data) {
      var result = $.parseJSON(data);
      //console.log(result);
      $("#full_receipt_html").val(result.consolidated_receipt_html);

    }
  });

  }


function terms_condition() {
  $('#terms_condition').modal('show');
  construct_terms_condition();
}

function construct_terms_condition(){
  $("#terms_conditions").val('');
    var blueprintId = $('#blueprintid_terms_condition').val();
    //alert(blueprintId);
    $.ajax({
    url: '<?php echo site_url('feesv2/fees_blueprint/term_condition_feev2_blueprint'); ?>',
    type: 'post',
    data:{'blueprintId': blueprintId},
    success: function(data) {
      var result = $.parseJSON(data);
      //console.log(result.terms_conditions);
      
      $("#terms_conditions").code(result.terms_conditions);

  
    }
  });

}




function view_component() {
  $('#view_components').modal('show');
  //$("#view_components").val('');
    var blueid = $('#blueprintid').val();
    //console.log(blueid);
    $.ajax({
    url: '<?php echo site_url('feesv2/fees_blueprint/get_component_detailsby'); ?>',
    type: 'post',
    data: {'blueid': blueid},
    success: function(data) {
      var view_comp = $.parseJSON(data)
        //console.log(view_comp);
        component_detailsby(view_comp);
    },
    error: function(xhr, status, error) {
        console.error('Error:', status, error);
    }
});

  
}

function component_detailsby(view_comp){
  //console.log(view_comp);
  var html="";
  html+='<div class="panel-body">';
  html+='<table class="table table-bordered">';
  html+='<thead>';
  html+='<tr>';
  html+='<th>#</th>';
  html+='<th>Components</th>';
  html+='<th>Vendor Code</th>';
  html+='<th>Account Number</th>';
  html+=' </tr></thead>';
  for(var i = 0; i < view_comp.length; i++){
    
    html+='<tbody>';
    html+='<tr>';
    html+='<td> ' + (i+1) + ' </td>';
    html+='<td> ' + view_comp[i].blueprint_name + ' </td>';
    html+='<td> ' + view_comp[i].vendor_code + ' </td>';
    html+='<td> ' + view_comp[i].account + ' </td>';
    html+='</tr>';
    i++;
  }
  html+='</tbody></table>';


  $('#components_view').html(html);


}

</script>

<!--Receipt Book -->
<div class="modal fade" id="add_receipt_book" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" data-backdrop="static" aria-hidden="true">
  <div class="modal-dialog" role="document" style="width:50%; margin-left: auto;margin-right:auto; margin-top:5%;">
    <div class="card cd_border">
      <div class="card-header panel_heading_new_style_staff_border">
        <div class="row m-0">
          <div class="col-md-9">
            <h3 class="card-title panel_title_new_style_staff mb-0"><strong>Receipt book</strong></h3>
          </div>
          <div class="col-md-3  d-flex justify-content-end">
            <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
          </div>
        </div>
      </div>

      <div class="panel-body">
        <div class="form-group">
          <label class="col-md-3 col-xs-12 control-label">Select Receipts</label>
          <div class="col-md-8 col-xs-12">
            <div class="input-group">
              <select class="form-control" id="receipt_book" name="receipt_book">
                <option value="">Select Receipt</option>
                <!-- <?php //foreach ($receiptbook as $key => $book) { ?>
                  <option <?php  //if($book->id == $val['receipt_book_id']) echo 'selected' ?> value="<?php // echo $book->id ?>"><?php // echo $book->receipt ?></option>                                          
                <?php // } ?> -->
              </select>
            </div>
          </div>
        </div>                                    
      </div>

      <div class="panel-footer new-footer">
        <button id="submitbutton" onclick="submit_receipt_book()" class="btn btn-info">Submit</button>
        <button type="button" class="btn btn-warning" data-dismiss="modal">Cancel</button>
      </div>

    </div>
  </div>
</div>
<!--End Receipt Book -->



<!--Fee Receipt html -->
<div class="modal fade" id="html_recepit" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" data-backdrop="static" aria-hidden="true">
  <div class="modal-dialog" role="document" style="width:90%; margin-left: auto;margin-right:auto; margin-top:5%;">
    <div class="card cd_border">
      <div class="card-header panel_heading_new_style_staff_border">
        <div class="row m-0">
          <div class="col-md-9">
            <h3 class="card-title panel_title_new_style_staff mb-0"><strong>Fee Receipt html</strong></h3>
          </div>
          <div class="col-md-3  d-flex justify-content-end">
            <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
          </div>
        </div>
      </div>

      <form id="subForm" method="post" enctype="multipart/form-data" data-parsley-validate="" action="<?php echo base_url('feesv2/fees_blueprint/receipt_html_format_assign_blueprint_new'); ?>">

      <div class="panel-body">
        <div class="form-group">
        
          <input type="hidden" name="blueprintid_receipt_html" id="blueprintid_receipt_html">

          <textarea class="form-control" rows="25" name="receipt_html" id="full_receipt_html">
          
           

          
       
          </textarea>
          
        </div>                                    
      </div>

      <div class="panel-footer new-footer">
        <button id="submitBtn" name="SuBmit" type="submit" class="btn btn-primary" >Add</button>
        <button type="button" class="btn btn-warning" data-dismiss="modal">Cancel</button>
      </div>

      </form>

    </div>
  </div>
</div>
<!-- End Fee Receipt html -->

<!-- Full Fee Receipt html -->
<div class="modal fade" id="full_fee_html_recepit" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" data-backdrop="static" aria-hidden="true">
  <div class="modal-dialog" role="document" style="width:90%; margin-left: auto;margin-right:auto; margin-top:5%;">
    <div class="card cd_border">
      <div class="card-header panel_heading_new_style_staff_border">
        <div class="row m-0">
          <div class="col-md-9">
            <h3 class="card-title panel_title_new_style_staff mb-0"><strong>Full Fee Receipt html</strong></h3>
          </div>
          <div class="col-md-3  d-flex justify-content-end">
            <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
          </div>
        </div>
      </div>

      <form id="subForm" method="post" enctype="multipart/form-data" data-parsley-validate="" action="<?php echo base_url('feesv2/fees_blueprint/'); ?>">

      <div class="panel-body">
        <div class="form-group">
        
          <input type="hidden" name="blueprintid_receipt_html" id="blueprintid_receipt_html">

          <textarea class="form-control" rows="25" name="receipt_html" id="full_fee_html_recepit">
          
           

          
       
          </textarea>
          
        </div>                                    
      </div>

      <div class="panel-footer new-footer">
        <button id="submitBtn" name="SuBmit" type="submit" class="btn btn-primary" >Add</button>
        <button type="button" class="btn btn-warning" data-dismiss="modal">Cancel</button>
      </div>

      </form>

    </div>
  </div>
</div>
<!-- End Full Fee Receipt html -->





<div class="modal fade" id="terms_condition" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" data-backdrop="static" aria-hidden="true">
  <div class="modal-dialog" role="document" style="width:90%; margin-left: auto;margin-right:auto; margin-top:5%;">
    <div class="card cd_border">
      <div class="card-header panel_heading_new_style_staff_border">
        <div class="row m-0">
          <div class="col-md-9">
            <h3 class="card-title panel_title_new_style_staff mb-0"><strong>Terms & Condition</strong></h3>
          </div>
          <div class="col-md-3  d-flex justify-content-end">
            <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
          </div>
        </div>
      </div>
      <form id="subForm" method="post" enctype="multipart/form-data" data-parsley-validate="" action="<?php echo base_url('feesv2/fees_blueprint/terms_conditions_assign_blueprint_new'); ?>">

          <input type="hidden" name="blueprintid_terms_condition" id="blueprintid_terms_condition">

      <div class="panel-body">
        <div class="form-group">
        

          <textarea class="form-control summernote"  name="terms_conditions" style="height: 100px;" id="terms_conditions"></textarea>
          
        </div>                                    
      </div>

      <div class="panel-footer new-footer">
        <button id="submitBtn" name="SuBmit" type="submit" class="btn btn-primary" >Submit</button>
        <button type="button" class="btn btn-danger " data-dismiss="modal">Cancel</button>
      </div>
      </form>

    </div>
  </div>
</div>


<!-- Display Components Data -->
<div class="modal fade" id="view_components" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" data-backdrop="static" aria-hidden="true">
  <div class="modal-dialog" role="document" style="width:90%; margin-left: auto;margin-right:auto; margin-top:5%;">
    <div class="card cd_border">
      <div class="card-header panel_heading_new_style_staff_border">
        <div class="row m-0">
          <div class="col-md-9">
            <h3 class="card-title panel_title_new_style_staff mb-0"><strong>View Components</strong></h3>
          </div>
          <div class="col-md-3  d-flex justify-content-end">
            <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
          </div>
        </div>
      </div>
<div id="components_view" name="components_view">

</div>
                                        
    

      <div class="panel-footer new-footer" >
        <button type="button" class="btn btn-danger float-right"    data-dismiss="modal">Close</button>
      </div>
      </div>
    </div>
 
</div>
<!-- End Display Components Data -->
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/5.0.0/normalize.min.css">

<script type="text/javascript">
  $('#staffdiscount_amount_algo').on('change',function(){
    var algo = $('#staffdiscount_amount_algo').val();
    if (algo == 'discount_if_full_paid_num' || algo =='discount_if_full_paid_p') {
      $('#staff_discountAmount').show();
    }else{
      $('#staff_discountAmount').hide();
      $('#staff_discount_amountId').val('');
    }
  });

$('#discount_amount_algo').on('change',function(){
    var algo = $('#discount_amount_algo').val();
    if (algo == 'discount_if_full_paid_num' || algo == 'discount_if_full_paid_p') {
      $('#discountAmount').show();
    }else{
      $('#discountAmount').hide();
      $('#discount_amountId').val('');
    }
  });
  

  $('#fine_amount_algo').on('change',function(){
    var algo = $('#fine_amount_algo').val();

    // Hide all sections first
    $('#fineAmount').hide();
    $('#fineJsonHelp').hide();
    $('#fine_amount_params').val('');

    if (algo == 'fine_per_day' || algo == 'fixed_fine' || algo == 'fine_per_week' || algo == 'fine_per_month' ||
        algo == 'fine_json_day' || algo == 'fine_json_week' || algo == 'fine_json_month' ||
        algo == 'fine_json_bimonthly' || algo == 'fine_date_range' || algo == 'fine_cumulative_range') {
      $('#fineAmount').show();

      // Set placeholder and show help for JSON types
      if (algo.includes('json') || algo.includes('range')) {
        $('#fineJsonHelp').show();
        var placeholder = '';
        switch(algo) {
          case 'fine_json_day':
            placeholder = '[{"days":30,"fine":10},{"days":20,"fine":20},{"days":"no_day","fine":30}]';
            break;
          case 'fine_json_week':
            placeholder = '[{"weeks":4,"fine":50},{"weeks":"no_week","fine":100}]';
            break;
          case 'fine_json_month':
            placeholder = '[{"months":1,"fine":200},{"months":"no_month","fine":300}]';
            break;
          case 'fine_json_bimonthly':
            placeholder = '[{"bimonth":2,"fine":400},{"bimonth":"no_bimonth","fine":500}]';
            break;
          case 'fine_date_range':
            placeholder = '[{"start_date":"2025-01-01","end_date":"2025-07-07","fine":30},{"start_date":"2025-07-08","end_date":"2025-07-14","fine":60}]';
            break;
          case 'fine_cumulative_range':
            placeholder = '[{"start_date":"2025-01-01","end_date":"2025-07-07","fine":30},{"start_date":"2025-07-08","end_date":"2025-07-14","fine":60}]';
            break;
        }
        $('#fine_amount_params').attr('placeholder', placeholder);
      } else {
        $('#fine_amount_params').attr('placeholder', 'Fine amount Params');
      }
    }
  });

  $('#discount_amount_algo_fine').on('change',function(){
    var algo = $('#discount_amount_algo_fine').val();
    if (algo == 'discount_if_full_paid_num') {
      $('#discountAmount_fine').show();
    }else if(algo == 'discount_if_full_paid_p'){
      $('#discountAmount_fine').show();
    }else{
      $('#discountAmount_fine').hide();
      $('#discount_amountId_fine').val('');
    }
  });



  $('#fine_amount_algo_fine').on('change',function(){
    var algo = $('#fine_amount_algo_fine').val();
    if (algo == 'fine_per_day' || algo == 'fine_per_week' || algo == 'fine_per_month' || algo == 'fixed_fine' ||
        algo == 'fine_json_day' || algo == 'fine_json_week' || algo == 'fine_json_month' ||
        algo == 'fine_json_bimonthly' || algo == 'fine_date_range' || algo == 'fine_cumulative_range') {
      $('#fineAmount_fine').show();

      // Set placeholder for JSON types
      if (algo.includes('json') || algo.includes('range')) {
        var placeholder = '';
        switch(algo) {
          case 'fine_json_day':
            placeholder = '[{"days":30,"fine":10},{"days":20,"fine":20},{"days":"no_day","fine":30}]';
            break;
          case 'fine_json_week':
            placeholder = '[{"weeks":4,"fine":50},{"weeks":"no_week","fine":100}]';
            break;
          case 'fine_json_month':
            placeholder = '[{"months":1,"fine":200},{"months":"no_month","fine":300}]';
            break;
          case 'fine_json_bimonthly':
            placeholder = '[{"bimonth":2,"fine":400},{"bimonth":"no_bimonth","fine":500}]';
            break;
          case 'fine_date_range':
            placeholder = '[{"start_date":"2025-01-01","end_date":"2025-07-07","fine":30},{"start_date":"2025-07-08","end_date":"2025-07-14","fine":60}]';
            break;
          case 'fine_cumulative_range':
            placeholder = '[{"start_date":"2025-01-01","end_date":"2025-07-07","fine":30},{"start_date":"2025-07-08","end_date":"2025-07-14","fine":60}]';
            break;
        }
        $('#fine_amount_params_fine').attr('placeholder', placeholder);
      } else {
        $('#fine_amount_params_fine').attr('placeholder', 'Fine amount Params');
      }
    }else{
      $('#fineAmount_fine').hide();
      $('#fine_amount_params_fine').val('');
    }
  });

 
</script>


<style type="text/css">
    .new_circleShape_res {
    padding: 6px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 16px;
    height: 30px !important;
    width: 30px !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}

    .widthadjust {
        width: 600px;
        margin: auto;
    }

    .green_button {
        border-color: #36d236;
        border-width: 2px;
    }
    .green_icon {
        color: #36d236;
    }
    .red_button {
        border-color: red;
        border-width: 2px;
    }
    .red_icon {
        color: red;
    }
    .dropbtn {
    background-color: #4CAF50;
    color: white;
    padding: 10px;
    font-size: 16px;
    border: none;
    cursor: pointer;
    }

/* Style the dropdown content (hidden by default) */
    .dropdown-content {
        display: none;
        position: absolute;
        background-color: #f9f9f9;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        z-index: 1;
        right: 0;
        top:37px;
        border: 1px solid white;
        cursor: pointer;
        border-radius: 8px;
    }

    /* Show the dropdown content when the dropdown button is hovered over */
    .dropdown:hover .dropdown-content {
        display: block;
    }

    /* Style the dropdown links */
    .dropdown-content a {
        color: black;
        padding: 12px 16px;
        text-decoration: none;
        display: block;
    }

    /* Change color on hover */
    .dropdown-content a:hover {
        background-color: #ddd;
    }
    .loaderclass {
  border: 8px solid #eee;
  border-top: 8px solid #7193be;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  animation: spin 2s linear 4;
  margin-top: 25%;
  margin-left: 45%;
  position: absolute;
  z-index: 99999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


.list-group-item.active{
    background-color: #bcc1c4;
    border-color: #ebf3f9;
    
  }
</style>

