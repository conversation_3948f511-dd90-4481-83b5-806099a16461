<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('student_nc/menu'); ?>">Student Non-Compliance</a></li>
  <li>Balance NC Report</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('student_nc/menu/index') ?>" class="control-primary">
              <span class="fa fa-arrow-left"></span>
            </a>
            Balance NC Report
          </h3>
        </div>
      </div>
    </div>

    <div class="col-md-12">
      <div class="col-md-3">
        <select class="form-control" name="class_section" id="class_section" onChange="get_student_nc_data()">
          <option value="-1">Choose Section</option>
          <?php
          foreach ($section_list as $key => $val) { ?>
            <option value="<?php echo $val->id ?>"><?php echo $val->class_name ?></option>
          <?php }
          ?>
        </select>
      </div>

      <div class="col-md-3">
        <select class="form-control" name="acad_year" id="acad_year" onChange="get_student_nc_data()">
          <option value="">All</option>
          <?php if(!empty($acad_years)){ foreach($acad_years as $key => $val) {
            $selected = ''; if($val->acad_year_id == $this->acad_year->getAcadYearId()) { $selected = 'selected'; } ?>
            <option value="<?= $val->acad_year_id ?>" <?= $selected ?>><?= $val->acad_year ?></option>
          <?php }} ?>
        </select>
      </div>

    </div>
    <div class="card-body pt-1" id="show_balance_nc_div" style="width:100%;margin-top:20px">
    </div>
    <div id="msg">

    </div>
  </div>
</div>

<script type="text/javascript">
  let msg = `
  <div style="color:red;text-align:center;
    color: #783c3c;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin: 1px;
    padding: 10px;
    font-size: 14px;
    background: #ff9999;">
      No NC's To Show
    </div>
  `;


  $(document).ready(function() {
    $("#message").css('display', 'none')
    // get_student_nc_data();


  })

  function get_student_nc_data() {
    var section_id = $("#class_section").val();
    var acad_year = $('#acad_year').val();
    const section_text = $("#class_section option:selected").text();
    if(section_id == '-1'){
      return false;
    }
    $.ajax({
      url: '<?php echo site_url('student_nc/nc_reports/get_balance_nc_data'); ?>',
      type: 'post',
      data: {
        'section_id': section_id,'acad_year':acad_year
      },
      success: function(data) {
        let datas = $.parseJSON(data);
        let html = construct_balance_nc_report(datas)
        if (datas.nc_report.length) {
          $('#show_balance_nc_div').html(html);
          $('#msg').hide();
        } else {
          $('#show_balance_nc_div').html("");
          $('#msg').show();
          $('#msg').html(msg);
        }
        $('#nc_report_table').DataTable({
              ordering: false,
              scrollY: '40vh',
              language: {
                  search: "",
                  searchPlaceholder: "Enter Search..."
              },
              lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
              pageLength: 10,
              dom: 'lBfrtip',
              buttons: [
                  {
                      extend: 'excelHtml5',
                      text: 'Excel',
                      filename: section_text+' balance nc report',
                      className: 'btn btn-info'
                  },
                  {
                      extend: 'print',
                      text: 'Print',
                      filename: section_text+' balance nc report',
                      className: 'btn btn-info'
                  },
                  {
                      extend: 'pdfHtml5',
                      text: 'PDF',
                      filename: section_text+' balance nc report',
                      className: 'btn btn-info'
                  }
              ]
          });
      }
    })
  }

  function construct_balance_nc_report(nc_data) {
    console.log(nc_data);
    var html = '';

    html = `
          <table id="nc_report_table" class="table table-bordered">
          <thead>
            <tr class="table-primary">
                <th>Sl #</th>
                <th>Name</th>
                <th>Balance NCs</th>
            </tr>
          </thead>
          <tbody>
        `;

    nc_data.nc_report.forEach((data, i) => {
      html += `
          <tr>
            <td>${++i}</td>
            <td>${data['stdName']}</td>
            <td>${data['balance_nc']}</td>
        </tr>
          `
    })

    html += `</tbody></table>`

    return html;






    // </table>

  }
</script>
<style>
  .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
  /* styles for DataTable end */
  .dataTables_scrollBody{
    margin-top: -13px;
  }
</style>