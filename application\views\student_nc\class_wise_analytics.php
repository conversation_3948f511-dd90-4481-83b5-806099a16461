<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('student_nc/menu'); ?>">Student Non-Compliance</a></li>
  <li>Class Wise NC Report</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('student_nc/menu/index') ?>" class="control-primary">
              <span class="fa fa-arrow-left"></span>
            </a>
            Class Wise NC Report
          </h3>
        </div>
      </div>
    </div>

    <div class="col-md-12">
      <div class="col-md-3">
        <select class="form-control" name="mode" id="mode_name" onChange="get_mode_wise()">
          <option value="-1">Select Mode</option>
          <option value="class">Class Wise</option>
          <option value="section">Section Wise</option>
        </select>
      </div>
      <div class="col-md-3">
        <select class="form-control" name="class_name" id="class_name" onChange="get_class_wise_nc_data()">
        </select>
      </div>
      <div class="col-md-3">
        <select class="form-control" name="acad_year" id="acad_year" onChange="get_class_wise_nc_data()">
        <option value="">All</option>
          <?php if(!empty($acad_years)){ foreach($acad_years as $key => $val) {
            $selected = ''; if($val->acad_year_id == $this->acad_year->getAcadYearId()) { $selected = 'selected'; } ?>
            <option value="<?= $val->acad_year_id ?>" <?= $selected ?>><?= $val->acad_year ?></option>
          <?php }} ?>
        </select>
      </div>
      <div class="col-md-3" style="position: relative;bottom: 5px;">
        <input style="position: relative;height: 22px;width: 23px;top: 7px;" type="checkbox" id="balance" name="balance" value="balance" onChange="balance()">
        <label style="border-radius: 2px;position: relative;bottom: 0;" class="" for="balance"> Show Only Balance NCs</label><br>
      </div>
    </div>

    <div class="card-body pt-1" id="show_class_wise_nc_div" style="width:100%;">
    </div>
    <div id="msg">

    </div>
  </div>
</div>

<script type="text/javascript">
  let msg = `
  <div style="color:red;text-align:center;
    color: #783c3c;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin: 1px;
    padding: 10px;
    font-size: 14px;
    background: #ff9999;">
      No NC's To Show
    </div>
  `;
  let i = 0;
  let filterr = "false";

  function balance() {

    const bal = $("#balance").val();
    if (++i % 2 !== 0) {
      var class_id = $("#class_name").val();
      $.ajax({
        url: '<?php echo site_url('student_nc/nc_reports/get_class_wise_nc_analytics'); ?>',
        type: 'post',
        data: {
          'class_id': class_id,
          'filter': "true"
        },
        success: function(data) {
          let datas = $.parseJSON(data);
          let html = construct_balance_nc_report(datas)
          if(datas.class_data.length) {
            $('#show_class_wise_nc_div').html(html);
            $('#nc_report_table').DataTable({
              ordering: false,
              scrollY: '40vh',
              language: {
                  search: "",
                  searchPlaceholder: "Enter Search..."
              },
              lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
              pageLength: 10,
              dom: 'lBfrtip',
              buttons: [
                  {
                      extend: 'excelHtml5',
                      text: 'Excel',
                      filename: 'student_non_complaince_report',
                      className: 'btn btn-info'
                  },
                  {
                      extend: 'print',
                      text: 'Print',
                      filename: 'student_non_complaince_report',
                      className: 'btn btn-info'
                  },
                  {
                      extend: 'pdfHtml5',
                      text: 'PDF',
                      filename: 'student_non_complaince_report',
                      className: 'btn btn-info'
                  }
              ]
          });
          }
          // $('#msg').html(msg);
        }
      })

    } else {
      get_class_wise_nc_data();
    }
  }
  $(document).ready(function() {
    // get_class_wise_nc_data();
  })
  function get_mode_wise() {
    let mode_name = $("#mode_name").val();
    if (mode_name == 'class')  {
      generate_class_wise_list();
    }else if(mode_name == 'section') {
      generate_section_wise_list();
    } else {
      let html = `<option value="-1">Select Mode First</option>`;
      $('#class_name').html(html);
    }
  }
  function generate_class_wise_list() {
    let class_list = '<?php echo json_encode($class_list) ?>';
    let parsed_data = $.parseJSON(class_list);
    let html = `<option value="-1">Select Class</option>`;
    for(let i=0; i<parsed_data.length;i++){
      html +=`<option  value="${parsed_data[i].classId}">${parsed_data[i].className} </option>`;
    }
    $('#class_name').html(html);
   
  }
  function generate_section_wise_list() {
    let data = '<?php echo json_encode($grade_list) ?>';
    let parsed_data = $.parseJSON(data);
    let html = `<option value="-1">Select Section</option>`;
    for(let i=0; i<parsed_data.length;i++){
      html +=`<option  value="${parsed_data[i].id}">${parsed_data[i].class_name} </option>`;
    }
    $('#class_name').html(html);
  }
  function get_class_wise_nc_data() {
    $("#balance").prop("checked", false);
    const class_id = $("#class_name").val();
    let mode_name = $("#mode_name").val();
    var acad_year = $('#acad_year').val();
    const class_text = $("#class_name option:selected").text();
    if(class_id == '-1' || class_id == null){
      return false;
    }
    $.ajax({
      url: '<?php echo site_url('student_nc/nc_reports/get_class_wise_nc_analytics'); ?>',
      type: 'post',
      data: {
        'class_id': class_id,
        'mode_name':mode_name,
        'filter': "false",
        'acad_year':acad_year
      },
      success: function(data) {
        datas = $.parseJSON(data);
         //console.log(datas);
        let html = construct_balance_nc_report(datas)
        if(datas.class_data.length) {
          $('#msg').html("");
          $('#show_class_wise_nc_div').html(html);
        }else{
          $('#show_class_wise_nc_div').html("");
          $('#msg').html(msg);
        }
        $('#nc_report_table').DataTable({
            ordering: false,
            scrollY: '40vh',
            language: {
                search: "",
                searchPlaceholder: "Enter Search..."
            },
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
            pageLength: 10,
            dom: 'lBfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    filename: class_text +' NC Report',
                    className: 'btn btn-info'
                },
                {
                    extend: 'print',
                    text: 'Print',
                    filename: class_text +' NC Report',
                    className: 'btn btn-info'
                },
                {
                    extend: 'pdfHtml5',
                    text: 'PDF',
                    filename: class_text +' NC Report',
                    className: 'btn btn-info'
                }
            ]
        });
      }
    })
  }

  function construct_balance_nc_report(nc_data) {
     //console.log(nc_data);
    var html = '';

    html = `
          <table id="nc_report_table" class="table table-bordered">
          <thead>
            <tr>
                <th>Sl.No</th>
                <th>Name</th>
                <th>Total</th>
                <th>Served</th>
                <th>No Penalty</th>
                <th>Grace</th>
                <th>Penalties To Be Served</th>
            </tr>
            </thead>
            <tbody>
        `;

    nc_data.class_data.forEach((data, i) => {
      // console.log(data['student_id'] + " " + data['roll_no'] + " " + data['section_name'] + " " + data['student_name'] + " " + data['balance_nc']);
      html += `
          <tr>
            <td>${i+1}</td>
            <td>${data['stdName']}</td>
            <td>${data['total_balance_nc']}</td>
            <td>${data['served']}</td>
            <td>${data['no_penalty']}</td>
            <td>${data['grace']}</td>
            <td>${data['balance']}</td>
        </tr>
          `
    })
    html += `</tbody></table>`;
    return html;
  }
</script>
<style>
  .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
  /* styles for DataTable end */
  .dataTables_scrollBody{
    margin-top: -13px;
  }
</style>