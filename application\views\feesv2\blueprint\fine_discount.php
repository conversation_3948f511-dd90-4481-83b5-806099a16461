<!-- START BREADCRUMB -->
<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard') ?>">Fee Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_blueprint');?>">Fee Blueprint</a></li>
  <li class="active">Fine and discount</li>
</ul>

<div class="page-content-wrap">
  <form enctype="multipart/form-data" method="post" id="demo-form" action="<?php echo site_url('feesv2/fees_blueprint/update_fine_discount_alogo');?>" data-parsley-validate="" class="form-horizontal">
  <input type="hidden" name="feev2_blueprint_id" value="<?= $feev2_blueprint_id ?>">
  <div class="row">
    <div class="col-md-8">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title">Add Fine and Discount</h3>
        </div>

        <div class="panel-body">
          
           <div class="form-group" id="staff_discountAmount" style="display: none">
              <label class="col-md-2 control-label" for="discount_amount_algo">Discount</label>
            <div class="col-md-7"> 
              <input type="number" placeholder="Amount or Percentage" id="discount_amountId" class="form-control" name="discount_amount">
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="discount_amount_algo"> Discount Algorithm</label>  
            <div class="col-md-7">
              <select class="form-control" name="discount_amount_algo" id="discount_amount_algo" >
                <option value="none">None</option>
                <option value="manual_p">Manually Enter Percentage</option>
                <option value="manual_num">Manually Enter Amount</option>
                <option value="discount_if_full_paid_num">Discount if full paid number</option>
                <option value="discount_if_full_paid_p">Discount if full paid percentage</option>
                <option value="discount_if_full_paid_json">Discount in JSON Format</option>
              </select>
            </div>
          </div>


          <div class="form-group" id="discountAmount" style="display: none">
              <label class="col-md-2 control-label" for="discount_amountId">Discount</label>
            <div class="col-md-7"> 
              <input type="number" placeholder="Amount or Percentage" id="discount_amountId" class="form-control" name="discount_amount">
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="allocation_algo">Allocation Algorithm</label>
            <div class="col-md-7">
              <select class="form-control" name="allocation_algo">
                <option value="none">None</option>
                <option value="equal">Equal across all installments</option>
                <option value="custom">Custom</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-2 control-label" for="algo_params">Algorithm Parameters</label>  
            <div class="col-md-7">
              <input name="algo_params" id="allocation_params" class="form-control" placeholder="Algorithm Params" type="text"/>
            </div>
          </div>

            <div class="form-group">
            <label class="col-md-2 control-label" for="fine_amount_algo"> Fine Amount Algo</label>  
            <div class="col-md-7">
              <select class="form-control" name="fine_amount_algo" id="fine_amount_algo">
              <option value="none">None</option>
              <option value="manually_enter">Manually entry</option>
              <option value="fine_per_day">Fine per Day</option>
              <option value="fine_per_week">Fine per Week</option>
              <option value="fine_per_month">Fine per Month</option>
              <option value="fixed_fine">Fixed Fine</option>
              <option value="fine_json_bimonthly">Use JSON (Bimonthly)</option>
              <option value="fine_date_range">Date Range Fine</option>
              </select>
            </div>
            </div>

            <div class="form-group" id="previousfineAmount" style="display: none">
            <label class="col-md-2 control-label">Assigned Amount</label>  
            <div class="col-md-7">
              <input id="previous_amount_assinged" readonly class="form-control" type="text"/>
            </div>
            </div>

            <div class="form-group" id="fineAmount" style="display: none">
            <label class="col-md-2 control-label" for="algo_params">Fine Amount</label>  
            <div class="col-md-7">
              <input name="fine_amount_params" id="fine_amount_params" class="form-control" placeholder="Fine amount Params" type="text"/>
            </div>
            </div>

            <div class="form-group" id="fineJsonHelp">
            <label class="col-md-2 control-label"></label>
            <div class="col-md-7">
              <div class="alert alert-info" style="margin-bottom:0;">
              <strong>JSON Format Examples:</strong><br>
              <b>Bimonthly:</b> <code>[{"bimonth":2,"fine":400}]</code><br>
              <b>Date Range:</b> <code>[{"start_date":"YYYY-MM-DD","end_date":"YYYY-MM-DD","fine":30},{"start_date":"YYYY-MM-DD","end_date":"YYYY-MM-DD","fine":60}]</code><br>
              </div>
            </div>
            </div>

        </div>
        <div class="panel-footer">
          <center>
            <input type="submit" class="btn btn-primary" value="Submit"/>
            <a href="<?php echo site_url('feesv2/fees_blueprint');?>" class="btn btn-warning">Cancel</a>
          </center>
        </div>
      </div>
    </div>
  </div>
  </form>
</div>

<script type="text/javascript">
  $('#discount_amount_algo').on('change',function(){
    var algo = $('#discount_amount_algo').val();
    if (algo == 'discount_if_full_paid_num') {
      $('#discountAmount').show();
    }else if(algo == 'discount_if_full_paid_p'){
      $('#discountAmount').show();
    }else{
      $('#discountAmount').hide();
      $('#discount_amountId').val('');
    }
  });

$('#staff_discount_amount_algo').on('change',function(){
    var algo = $('#staff_discount_amount_algo').val();
    if (algo == 'discount_if_full_paid_num') {
      $('#discountAmount').show();
    }else if(algo == 'discount_if_full_paid_p'){
      $('#staff_discountAmount').show();
    }else{
      $('#staff_discountAmount').hide();
      $('#discount_amountId').val('');
    }
  });

  $('#fine_amount_algo').on('change',function(){
    var algo = $('#fine_amount_algo').val();

    // Hide all sections first
    $('#fineAmount').hide();
    $('#previousfineAmount').hide();
    $('#fineJsonHelp').hide();
    $('#fine_amount_params').val('');

    if (algo == 'fine_per_day' || algo == 'fixed_fine' || algo == 'fine_per_week' || algo == 'fine_per_month') {
      get_previous_fixed_fine_amount();
      $('#fineAmount').show();
      $('#previousfineAmount').show();
    } else if (algo == 'fine_json_day' || algo == 'fine_json_week' || algo == 'fine_json_month' ||
               algo == 'fine_json_bimonthly' || algo == 'fine_date_range' || algo == 'fine_cumulative_range') {
      $('#fineAmount').show();
      $('#fineJsonHelp').show();
      // Set placeholder text based on selected JSON type
      var placeholder = '';
      switch(algo) {
        case 'fine_json_day':
          placeholder = '[{"days":30,"fine":10},{"days":20,"fine":20},{"days":"no_day","fine":30}]';
          break;
        case 'fine_json_week':
          placeholder = '[{"weeks":4,"fine":50},{"weeks":"no_week","fine":100}]';
          break;
        case 'fine_json_month':
          placeholder = '[{"months":1,"fine":200},{"months":"no_month","fine":300}]';
          break;
        case 'fine_json_bimonthly':
          placeholder = '[{"bimonth":2,"fine":400},{"bimonth":"no_bimonth","fine":500}]';
          break;
        case 'fine_date_range':
          placeholder = '[{"start_date":"2025-01-01","end_date":"2025-07-07","fine":30},{"start_date":"2025-07-08","end_date":"2025-07-14","fine":60}]';
          break;
        case 'fine_cumulative_range':
          placeholder = '[{"start_date":"2025-01-01","end_date":"2025-07-07","fine":30},{"start_date":"2025-07-08","end_date":"2025-07-14","fine":60}]';
          break;
      }
      $('#fine_amount_params').attr('placeholder', placeholder);
    }
  });

  function get_previous_fixed_fine_amount(){
    let bpId = '<?php echo $feev2_blueprint_id ?>';
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_blueprint/get_previous_fine_alog_amount'); ?>',
        type: "post",
        data:{'bpId':bpId},
        success: function (data) {
          var res = JSON.parse(data);
          $('#previous_amount_assinged').val(res.fine_amount_params);
        },
        error: function (err) {
          console.log(err);
        }
    });
  }
</script>