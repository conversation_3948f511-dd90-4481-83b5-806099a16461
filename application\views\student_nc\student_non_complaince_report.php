<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('student_nc/menu'); ?>">Student Non-Compliance</a></li>
  <li>Student NC Report</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="col-md-12 pl-0">
          <h3 class="card-title panel_title_new_style_staff" style="position: absolute;">
            <a class="back_anchor" href="<?php echo site_url('student_nc/menu/index') ?>" class="control-primary">
              <span class="fa fa-arrow-left"></span>
            </a>
            Student NC Report
          </h3>
          <div class="col-md-1" style="float:inline-end">
        <?php 
            $arr = array();
            foreach ($admissionStatusArr as $key => $value) { 
                $arr[$key] = $value; 
            }
        ?>
  
        <select name="admission_status" id="admission_status" title="Select Classes" class="form-control" onchange="get_student_class_sectionwise()">
            <!-- <option value="">Select Section</option> -->
            <?php foreach ($arr as $key => $class) { ?>
            <option <?php if(in_array($key, [2])) echo 'selected' ?> value="<?= $key ?>"><?php echo $class;?></option>
            <?php } ?>
        </select>
    </div>  
        </div>
      </div>
    </div>
    
    <div class="col-md-12">
      <div class="col-md-3">
        <select class="form-control" name="class_section" id="class_section" onChange="get_student_class_sectionwise()">
          <option value="-1">Select Section</option>
          <?php
          foreach ($section_list as $key => $val) { ?>
            <option value="<?php echo $val->id ?>"><?php echo $val->class_name ?></option>
          <?php }
          ?>
        </select>
      </div>
      <div class="col-md-3">
        <select class="form-control" name="student_name" id="student_id" onChange="get_student_nc_data()">
          <option value="">Select Student</option>
        </select>
      </div>
      <div class="col-md-3">
        <select class="form-control" name="acad_yr" id="acad_yr" onChange="get_student_nc_data()">
          <option value="">All</option>
          <?php if(!empty($acad_years)){ foreach($acad_years as $key => $val) {
            $selected = ''; if($val->acad_year_id == $this->acad_year->getAcadYearId()) { $selected = 'selected'; } ?>
            <option value="<?= $val->acad_year_id ?>" <?= $selected ?>><?= $val->acad_year ?></option>
          <?php }} ?>
        </select>
      </div>
      <div id="summery_ncs" class="summery_ncs" style="display: block;margin-top: 40px;margin-left:16px;">
        <table style="font-size: 15px;" id="summary_table">
          <tr>
            <th id="roll_no_title"></th>
            <th id="roll_no"></th>
          </tr>
          <tr>
            <th id="enrollment_no_title"></th>
            <th id="enrollment_no"></th>
          </tr>
          <tr>
            <th id="total_title" style="padding-right:5px;" id=""></th>
            <th id="total_ncs"></th>
          </tr>
          <tr>
            <th id="served_title" style="padding-right:5px;" id=""></th>
            <th id="served_ncs"></th>
          </tr>
          <tr>
            <th id="balance_title" style="padding-right:5px;" id=""></th>
            <th id="balance_ncs"></th>
          </tr>
        </table>
      </div>
      <div id="data_not_available" style="margin-left: 16px;">
      </div>
    </div>
    <div class="card-body pt-1" id="show_student_nc_div" style="width:100%;margin-top:20px">

    </div>
    <div id="msg">

    </div>

  </div>
</div>

<script type="text/javascript">
  let msg = `
  <div style="color:red;text-align:center;
    color: #783c3c;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin: 1px;
    padding: 10px;
    font-size: 14px;
    background: #ff9999;">
      No NC's To Show
    </div>
  `;

  function get_student_class_sectionwise() {
    $('#show_student_nc_div').hide();
    $('#summary_table').hide();
    var classSectionId = $('#class_section').val();
    var admission_status = document.getElementById('admission_status').value;
    console.log(admission_status);
    $.ajax({
      url: '<?php echo site_url('student_nc/nc_reports/get_student_by_class_section'); ?>',
      type: 'post',
      data: {
        'classSectionId': classSectionId,
        'admission_status' : admission_status
      },
      success: function(data) {
        var resData = $.parseJSON(data);
        var output = '<option value="">Select Student</option>';
        for (var i = 0; i < resData.length; i++) {
          output += '<option value=' + resData[i].std_id + '>' + resData[i].student_name + '</option>';
          // console.log(resData[i])
        }
        $('#student_id').html(output);
      }
    })
  }
  $(document).ready(function() {
    // get_student_nc_data();
  })

  function get_student_nc_data() {
    // let students_id = $("#student_id").val();
    let students_id = $("#student_id").val();
    var acad_yr = $('#acad_yr').val();
    if(students_id == ''){
      return false;
    }
    // console.log(students_id)
    $('#show_student_nc_div').show();
    $('#summary_table').show();

    const name_text = $("#student_id option:selected").text();
    

    $.ajax({
      url: '<?php echo site_url('student_nc/nc_reports/get_student_nc_data'); ?>',
      type: 'post',
      data: {
        'students_id': students_id,'acad_yr':acad_yr
      },
      success: function(data) {
        const datas = $.parseJSON(data);
        console.log(datas);

        if (datas.student_wise_nc_report.length) {
          document.getElementById("data_not_available").textContent = ``;
          document.getElementById("roll_no").textContent = `: ${students_id || "0"}`;
          document.getElementById("enrollment_no").textContent = `: ${datas.student_wise_nc_report[0].enrollment_number}`;
          let html = construct_balance_nc_report(datas)
          $('#show_student_nc_div').html(html);
          $('#nc_report_table').DataTable({
              ordering: false,
              scrollY: '40vh',
              language: {
                  search: "",
                  searchPlaceholder: "Enter Search..."
              },
              lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
              pageLength: 10,
              dom: 'lBfrtip',
              buttons: [
                  {
                      extend: 'excelHtml5',
                      text: 'Excel',
                      filename: name_text+' non complaince report',
                      className: 'btn btn-info'
                  },
                  {
                      extend: 'print',
                      text: 'Print',
                      filename: name_text+' non complaince report',
                      className: 'btn btn-info'
                  },
                  {
                      extend: 'pdfHtml5',
                      text: 'PDF',
                      filename: name_text+' non complaince report',
                      className: 'btn btn-info'
                  }
              ]
          });
          $('#msg').html('');

        } else {
          document.getElementById("roll_no").textContent = ` `;
          document.getElementById("enrollment_no").textContent = ` `;
          let html = ``
          $('#show_student_nc_div').html(html);
          document.getElementById("roll_no_title").textContent = ``;
          document.getElementById("enrollment_no_title").textContent = ` `;
          document.getElementById("total_title").textContent = ``;
          document.getElementById("served_title").textContent = ``;
          document.getElementById("balance_title").textContent = ``;
          document.getElementById("total_ncs").textContent = ``;
          document.getElementById("served_ncs").textContent = ``;
          document.getElementById("balance_ncs").textContent = ``;

          document.getElementById("data_not_available").textContent = ``;
          $('#msg').html(msg);
        }
      }
    })
  }

  function construct_balance_nc_report(student_data) {
    let total_ncs = 0;
    let served_ncs = 0;
    let balance_ncs = 0;
    let roll_no = 0;

    var html = '';

    html = `
          <table id="nc_report_table" class="table table-bordered ">
          <thead>
            <tr class="table-primary">
                <th>Sl</th>
                <th>Date</th>
                <th>NC Category</th>
                <th>Penalty Handled By</th>
                <th>Status</th>
                <th>Remarks</th>
            </tr>
          </thead>
          <tbody>
        `;

    let name = "";
    student_data.student_wise_nc_report.forEach((data, i) => {
      let dateFormat = data['Date'].split("-").reverse().join("-")
      total_ncs++;
      if (data['Status'] == "penalty") {
        balance_ncs++
      }

      if (data['Status'] != "penalty") {
        served_ncs++
      }

      name =
        html += `
          <tr>
            <td>${i+1}</td>
            <td>${dateFormat}</td>
            <td>${data['nc_category']}</td>
            <td>${data['Assigned_by']}</td>
            <td style="background:${data['Status']=="penalty"&&"#ff9999;" || "#afffaf;"}">${data['Status'].at(0).toUpperCase()}${data['Status'].slice(1)}</td>
            <td style=color:${data['Remarks']|| "grey"}>${data['Remarks']|| "NA"}</td>
        </tr>
          `
    })

    html += `</body></table>`;

    document.getElementById("roll_no_title").textContent = `Roll No`;
    document.getElementById("enrollment_no_title").textContent = `Enrollment Number`;
    document.getElementById("total_title").textContent = `Total Nc`;
    document.getElementById("served_title").textContent = `Served Nc`;
    document.getElementById("balance_title").textContent = `Balance Nc`;
    document.getElementById("total_ncs").textContent = `: ${total_ncs}`;
    document.getElementById("served_ncs").textContent = `: ${served_ncs}`;
    document.getElementById("balance_ncs").textContent = `: ${balance_ncs}`;

    return html;
  }
</script>
<style>
  .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
  /* styles for DataTable end */
  .dataTables_scrollBody{
    margin-top: -13px;
  }
</style>